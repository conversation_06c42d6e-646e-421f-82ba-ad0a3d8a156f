#include "dac.h"

// 定义PI常数（如果编译器没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 频率-幅度映射表生成函数 (100Hz-400kHz，动态计算)
// 使用函数替代静态数组以节省内存并支持高频范围
static float DAC_GetAmplitudeTableValue(float frequency)
{
    // 对于高频信号，使用简化的幅度计算
    if (frequency <= 3000.0f) {
        // 低频段使用原有的映射关系
        if (frequency <= 100.0f) return 4.615f;
        else if (frequency <= 200.0f) return 4.462f;
        else if (frequency <= 300.0f) return 4.325f;
        else if (frequency <= 500.0f) return 4.058f;
        else if (frequency <= 1000.0f) return 3.727f;
        else if (frequency <= 1500.0f) return 3.175f;
        else if (frequency <= 2000.0f) return 2.686f;
        else if (frequency <= 2500.0f) return 2.278f;
        else if (frequency <= 3000.0f) return 1.977f;
    } else {
        // 高频段使用线性衰减模型
        // 频率越高，需要的DAC幅度越小以保持输出恒定
        float freq_khz = frequency / 1000.0f;
        if (freq_khz <= 10.0f) return 1.5f;
        else if (freq_khz <= 50.0f) return 1.0f;
        else if (freq_khz <= 100.0f) return 0.8f;
        else if (freq_khz <= 200.0f) return 0.6f;
        else if (freq_khz <= 300.0f) return 0.5f;
        else return 0.4f;  // 400kHz及以上
    }
    return 1.0f;  // 默认值
}

// 正弦波查找表
static uint16_t sine_table[DAC_SINE_SAMPLES];

// 正弦波输出控制变量
float dac_output_frequency = 100.0f;    // 当前输出频率
uint8_t dac_output_enabled = 0;         // 输出使能标志（默认关闭）
uint8_t dac_user_enabled = 0;           // 用户使能标志（按钮控制）
float dac_amplitude_multiplier = 1.0f;  // 幅度倍数 (1.0, 1.1, 1.2, ..., 2.0)
static uint32_t sine_index = 0;         // 当前正弦波索引
static float phase_increment = 0.0f;    // 相位增量
static float current_phase = 0.0f;      // 当前相位
static float current_amplitude = 0.5f;  // 当前幅度

/**
 * @brief  初始化DAC通道1 (PA4)
 * @param  None
 * @retval None
 * @note   PA4配置为DAC_OUT1，12位分辨率
 */
void DAC_PA4_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能GPIOA和DAC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);
    
    // 配置PA4为模拟模式
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;        // 模拟模式
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;   // 无上下拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置DAC通道1
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_None;                    // 无触发
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;      // 无波形生成
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;        // 使能输出缓冲器
    
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);
    
    // 使能DAC通道1
    DAC_Cmd(DAC_Channel_1, ENABLE);
    
    // 设置初始输出值为1.65V (中间值)
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  生成正弦波查找表
 * @param  amplitude: 正弦波幅度
 * @retval None
 * @note   根据指定幅度生成正弦波查找表
 */
static void DAC_GenerateSineTable(float amplitude)
{
    uint32_t i;

    // 生成正弦波查找表
    for (i = 0; i < DAC_SINE_SAMPLES; i++)
    {
        float angle = (2.0f * M_PI * i) / DAC_SINE_SAMPLES;
        float sine_value = sinf(angle);

        // 将正弦波从[-1,1]映射到指定范围
        // 公式: voltage = 1.65V + amplitude * sin(angle)
        float voltage = DAC_OFFSET_VOLTAGE + (sine_value * amplitude);

        // 限制电压范围在0-3.3V之间
        if (voltage < DAC_MIN_VOLTAGE)
            voltage = DAC_MIN_VOLTAGE;
        if (voltage > DAC_MAX_VOLTAGE)
            voltage = DAC_MAX_VOLTAGE;

        // 转换为DAC值 (0-4095)
        sine_table[i] = (uint16_t)((voltage / DAC_VREF) * 4095.0f);
    }
}

/**
 * @brief  初始化DAC正弦波输出
 * @param  None
 * @retval None
 * @note   生成正弦波查找表，配置定时器
 */
void DAC_SineWave_Init(void)
{
    // 获取初始频率对应的幅度
    current_amplitude = DAC_GetAmplitudeForFrequency(dac_output_frequency);

    // 生成初始正弦波查找表
    DAC_GenerateSineTable(current_amplitude);

    // 初始化相位增量
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  根据频率获取对应的幅度
 * @param  frequency: 输入频率 (Hz)
 * @retval 对应的幅度值
 * @note   支持高频范围，使用动态幅度计算
 */
float DAC_GetAmplitudeForFrequency(float frequency)
{
    // 频率超过400kHz时返回0
    if (frequency > DAC_MAX_FREQ_HZ)
        return 0.0f;

    // 频率低于100Hz时使用100Hz的幅度
    if (frequency < DAC_BASE_FREQ)
        frequency = DAC_BASE_FREQ;

    // 使用动态幅度计算函数
    float amplitude_factor = DAC_GetAmplitudeTableValue(frequency);

    // 计算实际幅度：DAC峰峰值 = (1V / amplitude_factor) * multiplier
    // 这样经过外部电路放大后：DAC峰峰值 * amplitude_factor = 1V * multiplier
    float peak_to_peak = (1.0f / amplitude_factor) * dac_amplitude_multiplier;
    float amplitude = peak_to_peak / 2.0f;

    // 限制幅度不超过1.65V (确保电压在0-3.3V范围内)
    if (amplitude > DAC_OFFSET_VOLTAGE)
        amplitude = DAC_OFFSET_VOLTAGE;

    return amplitude;
}

/**
 * @brief  设置DAC通道1的数字值
 * @param  value: 12位数字值 (0-4095)
 * @retval None
 * @note   输出电压 = (value / 4095) * VREF+
 */
void DAC_SetChannel1Value(uint16_t value)
{
    // 限制值在12位范围内
    if (value > 4095)
        value = 4095;
    
    DAC_SetChannel1Data(DAC_Align_12b_R, value);
}

/**
 * @brief  设置DAC通道1的输出电压
 * @param  voltage: 输出电压值 (0.0V - 3.3V)
 * @retval None
 * @note   假设VREF+ = 3.3V
 */
void DAC_SetChannel1Voltage(float voltage)
{
    uint16_t dac_value;
    
    // 限制电压范围
    if (voltage < 0.0f)
        voltage = 0.0f;
    if (voltage > 3.3f)
        voltage = 3.3f;
    
    // 计算对应的DAC值
    dac_value = (uint16_t)((voltage / 3.3f) * 4095.0f);
    
    DAC_SetChannel1Value(dac_value);
}

/**
 * @brief  设置DAC正弦波输出频率
 * @param  frequency: 输出频率 (Hz)
 * @retval None
 * @note   当频率超过3kHz时，停止输出；频率改变时重新生成查找表
 */
void DAC_SetSineFrequency(float frequency)
{
    dac_output_frequency = frequency;

    // 检查频率限制
    if (frequency > DAC_MAX_FREQ_HZ)
    {
        DAC_StopSineOutput();
        return;
    }

    // 获取新频率对应的幅度
    float new_amplitude = DAC_GetAmplitudeForFrequency(frequency);

    // 如果幅度发生变化，重新生成查找表
    if (new_amplitude != current_amplitude)
    {
        current_amplitude = new_amplitude;
        DAC_GenerateSineTable(current_amplitude);
    }

    // 计算相位增量
    // 使用2.05MHz采样率以支持400kHz谐波 (根据奈奎斯特定理，至少需要800kHz)
    // 实际定时器配置：84MHz / 41 / 1 ≈ 2.048MHz
    float sample_rate = 2048780.0f;  // 2.05MHz采样率，提供足够的精度
    phase_increment = (frequency * DAC_SINE_SAMPLES) / sample_rate;

    // 如果用户使能且频率在允许范围内，启动输出
    if (dac_user_enabled && frequency > 0 && frequency <= DAC_MAX_FREQ_HZ)
    {
        DAC_StartSineOutput();
    }
    else
    {
        DAC_StopSineOutput();
    }
}

/**
 * @brief  启动DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StartSineOutput(void)
{
    if (dac_user_enabled && dac_output_frequency <= DAC_MAX_FREQ_HZ)
    {
        dac_output_enabled = 1;
        current_phase = 0.0f;
        sine_index = 0;
    }
}

/**
 * @brief  停止DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StopSineOutput(void)
{
    dac_output_enabled = 0;
    // 输出中间电压1.65V
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  更新DAC正弦波输出 (在定时器中断中调用)
 * @param  None
 * @retval None
 * @note   此函数应该在高频定时器中断中调用，使用线性插值提高精度
 */
void DAC_UpdateSineOutput(void)
{
    if (!dac_output_enabled)
        return;

    // 更新相位
    current_phase += phase_increment;

    // 相位回绕
    if (current_phase >= DAC_SINE_SAMPLES)
    {
        current_phase -= DAC_SINE_SAMPLES;
    }

    // 获取当前索引和小数部分
    uint32_t index = (uint32_t)current_phase;
    float fraction = current_phase - index;

    // 线性插值以提高高频信号质量
    uint16_t current_value = sine_table[index];
    uint16_t next_value = sine_table[(index + 1) % DAC_SINE_SAMPLES];

    // 计算插值结果
    uint16_t interpolated_value = current_value +
        (uint16_t)((float)(next_value - current_value) * fraction);

    // 输出插值后的正弦波值
    DAC_SetChannel1Value(interpolated_value);
}

/**
 * @brief  设置DAC幅度倍数
 * @param  multiplier: 幅度倍数 (1.0-2.0)
 * @retval None
 */
void DAC_SetAmplitudeMultiplier(float multiplier)
{
//    // 限制倍数范围在1.0-2.0之间
//    if (multiplier < 1.0f)
//        multiplier = 1.0f;
//    if (multiplier > 2.0f)
//        multiplier = 2.0f;

    dac_amplitude_multiplier = multiplier;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  切换到下一个幅度倍数
 * @param  None
 * @retval None
 * @note   循环：1.0 -> 1.1 -> 1.2 -> ... -> 2.0 -> 1.0
 */
void DAC_NextAmplitudeMultiplier(void)
{
    // 增加0.1倍数
    dac_amplitude_multiplier += 0.1f;

    // 如果超过2.0，回到1.0
    if (dac_amplitude_multiplier > 2.05f)
        dac_amplitude_multiplier = 1.0f;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  获取当前DAC幅度倍数
 * @param  None
 * @retval 当前幅度倍数
 */
float DAC_GetAmplitudeMultiplier(void)
{
    return dac_amplitude_multiplier;
}

/**
 * @brief  设置DAC用户使能状态
 * @param  enable: 1-使能, 0-禁用
 * @retval None
 */
void DAC_SetUserEnable(uint8_t enable)
{
    dac_user_enabled = enable;

    if (enable)
    {
        // 用户使能时，根据当前频率决定是否启动输出
        DAC_StartSineOutput();
    }
    else
    {
        // 用户禁用时，停止输出
        DAC_StopSineOutput();
    }
}

/**
 * @brief  获取DAC用户使能状态
 * @param  None
 * @retval 用户使能状态
 */
uint8_t DAC_GetUserEnable(void)
{
    return dac_user_enabled;
}

/**
 * @brief  使能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Enable(void)
{
    DAC_Cmd(DAC_Channel_1, ENABLE);
}

/**
 * @brief  失能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Disable(void)
{
    DAC_Cmd(DAC_Channel_1, DISABLE);
}

/**
 * @brief  DAC测试函数 - 输出不同电压值进行测试
 * @param  None
 * @retval None
 * @note   可以用万用表测量PA4引脚的电压变化
 */
void DAC_Test(void)
{
    // 测试不同的电压输出
    DAC_SetChannel1Voltage(0.0f);   // 0V
    delay_ms(1000);

    DAC_SetChannel1Voltage(1.65f);  // 1.65V (中间值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(3.3f);   // 3.3V (最大值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(0.8f);   // 0.8V
    delay_ms(1000);

    DAC_SetChannel1Voltage(2.5f);   // 2.5V
    delay_ms(1000);

    // 回到0V
    DAC_SetChannel1Voltage(0.0f);
}
