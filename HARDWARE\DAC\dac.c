#include "dac.h"

// 定义PI常数（如果编译器没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 频率-幅度映射表 (100Hz-3000Hz，每100Hz一个点)
static const float amplitude_table[DAC_FREQ_TABLE_SIZE] = {
    4.615, 4.462, 4.325, 4.058, 3.727,
    3.448, 3.175, 2.924, 2.686, 2.475,
    2.278, 2.117, 1.977, 1.876, 1.764,
    1.670, 1.574, 1.482, 1.398, 1.324,
    1.255, 1.192, 1.133, 1.073, 1.024,
    0.974, 0.9266, 0.8827, 0.8415, 0.825
};

// 正弦波查找表
static uint16_t sine_table[DAC_SINE_SAMPLES];

// 正弦波输出控制变量
float dac_output_frequency = 100.0f;    // 当前输出频率
uint8_t dac_output_enabled = 0;         // 输出使能标志（默认关闭）
uint8_t dac_user_enabled = 0;           // 用户使能标志（按钮控制）
float dac_amplitude_multiplier = 1.0f;  // 幅度倍数 (1.0, 1.1, 1.2, ..., 2.0)
static uint32_t sine_index = 0;         // 当前正弦波索引
static float phase_increment = 0.0f;    // 相位增量
static float current_phase = 0.0f;      // 当前相位
static float current_amplitude = 0.5f;  // 当前幅度

/**
 * @brief  初始化DAC通道1 (PA4)
 * @param  None
 * @retval None
 * @note   PA4配置为DAC_OUT1，12位分辨率
 */
void DAC_PA4_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能GPIOA和DAC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);
    
    // 配置PA4为模拟模式
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;        // 模拟模式
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;   // 无上下拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置DAC通道1
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_None;                    // 无触发
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;      // 无波形生成
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;        // 使能输出缓冲器
    
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);
    
    // 使能DAC通道1
    DAC_Cmd(DAC_Channel_1, ENABLE);
    
    // 设置初始输出值为1.65V (中间值)
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  生成正弦波查找表
 * @param  amplitude: 正弦波幅度
 * @retval None
 * @note   根据指定幅度生成正弦波查找表
 */
static void DAC_GenerateSineTable(float amplitude)
{
    uint32_t i;

    // 生成正弦波查找表
    for (i = 0; i < DAC_SINE_SAMPLES; i++)
    {
        float angle = (2.0f * M_PI * i) / DAC_SINE_SAMPLES;
        float sine_value = sinf(angle);

        // 将正弦波从[-1,1]映射到指定范围
        // 公式: voltage = 1.65V + amplitude * sin(angle)
        float voltage = DAC_OFFSET_VOLTAGE + (sine_value * amplitude);

        // 限制电压范围在0-3.3V之间
        if (voltage < DAC_MIN_VOLTAGE)
            voltage = DAC_MIN_VOLTAGE;
        if (voltage > DAC_MAX_VOLTAGE)
            voltage = DAC_MAX_VOLTAGE;

        // 转换为DAC值 (0-4095)
        sine_table[i] = (uint16_t)((voltage / DAC_VREF) * 4095.0f);
    }
}

/**
 * @brief  初始化DAC正弦波输出
 * @param  None
 * @retval None
 * @note   生成正弦波查找表，配置定时器
 */
void DAC_SineWave_Init(void)
{
    // 获取初始频率对应的幅度
    current_amplitude = DAC_GetAmplitudeForFrequency(dac_output_frequency);

    // 生成初始正弦波查找表
    DAC_GenerateSineTable(current_amplitude);

    // 初始化相位增量
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  根据频率获取对应的幅度
 * @param  frequency: 输入频率 (Hz)
 * @retval 对应的幅度值
 * @note   使用线性插值计算中间频率的幅度
 */
float DAC_GetAmplitudeForFrequency(float frequency)
{
    // 频率超过3kHz时返回0
    if (frequency > DAC_MAX_FREQ_HZ)
        return 0.0f;

    // 频率低于100Hz时使用100Hz的幅度
    if (frequency < DAC_BASE_FREQ)
        frequency = DAC_BASE_FREQ;

    // 计算在表中的位置
    float table_index = (frequency - DAC_BASE_FREQ) / DAC_FREQ_STEP;

    // 如果超出表范围，使用1V峰峰值
    if (table_index >= DAC_FREQ_TABLE_SIZE)
        return 0.5f;  // 1V峰峰值对应0.5V幅度

    // 获取整数部分和小数部分
    uint32_t index = (uint32_t)table_index;
    float fraction = table_index - index;

    // 线性插值
    float amplitude_factor;
    if (index >= DAC_FREQ_TABLE_SIZE - 1)
    {
        amplitude_factor = amplitude_table[DAC_FREQ_TABLE_SIZE - 1];
    }
    else
    {
        amplitude_factor = amplitude_table[index] +
                          fraction * (amplitude_table[index + 1] - amplitude_table[index]);
    }

    // 计算实际幅度：DAC峰峰值 = (1V / amplitude_factor) * multiplier
    // 这样经过外部电路放大后：DAC峰峰值 * amplitude_factor = 1V * multiplier
    float peak_to_peak = (1.0f / amplitude_factor) * dac_amplitude_multiplier;
    float amplitude = peak_to_peak / 2.0f;

    // 限制幅度不超过1.65V (确保电压在0-3.3V范围内)
    if (amplitude > DAC_OFFSET_VOLTAGE)
        amplitude = DAC_OFFSET_VOLTAGE;

    return amplitude;
}

/**
 * @brief  设置DAC通道1的数字值
 * @param  value: 12位数字值 (0-4095)
 * @retval None
 * @note   输出电压 = (value / 4095) * VREF+
 */
void DAC_SetChannel1Value(uint16_t value)
{
    // 限制值在12位范围内
    if (value > 4095)
        value = 4095;
    
    DAC_SetChannel1Data(DAC_Align_12b_R, value);
}

/**
 * @brief  设置DAC通道1的输出电压
 * @param  voltage: 输出电压值 (0.0V - 3.3V)
 * @retval None
 * @note   假设VREF+ = 3.3V
 */
void DAC_SetChannel1Voltage(float voltage)
{
    uint16_t dac_value;
    
    // 限制电压范围
    if (voltage < 0.0f)
        voltage = 0.0f;
    if (voltage > 3.3f)
        voltage = 3.3f;
    
    // 计算对应的DAC值
    dac_value = (uint16_t)((voltage / 3.3f) * 4095.0f);
    
    DAC_SetChannel1Value(dac_value);
}

/**
 * @brief  设置DAC正弦波输出频率
 * @param  frequency: 输出频率 (Hz)
 * @retval None
 * @note   当频率超过3kHz时，停止输出；频率改变时重新生成查找表
 */
void DAC_SetSineFrequency(float frequency)
{
    dac_output_frequency = frequency;

    // 检查频率限制
    if (frequency > DAC_MAX_FREQ_HZ)
    {
        DAC_StopSineOutput();
        return;
    }

    // 获取新频率对应的幅度
    float new_amplitude = DAC_GetAmplitudeForFrequency(frequency);

    // 如果幅度发生变化，重新生成查找表
    if (new_amplitude != current_amplitude)
    {
        current_amplitude = new_amplitude;
        DAC_GenerateSineTable(current_amplitude);
    }

    // 计算相位增量
    // 假设定时器中断频率为100kHz (每10us调用一次DAC_UpdateSineOutput)
    float sample_rate = 100000.0f;  // 100kHz采样率
    phase_increment = (frequency * DAC_SINE_SAMPLES) / sample_rate;

    // 如果用户使能且频率在允许范围内，启动输出
    if (dac_user_enabled && frequency > 0 && frequency <= DAC_MAX_FREQ_HZ)
    {
        DAC_StartSineOutput();
    }
    else
    {
        DAC_StopSineOutput();
    }
}

/**
 * @brief  启动DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StartSineOutput(void)
{
    if (dac_user_enabled && dac_output_frequency <= DAC_MAX_FREQ_HZ)
    {
        dac_output_enabled = 1;
        current_phase = 0.0f;
        sine_index = 0;
    }
}

/**
 * @brief  停止DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StopSineOutput(void)
{
    dac_output_enabled = 0;
    // 输出中间电压1.65V
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  更新DAC正弦波输出 (在定时器中断中调用)
 * @param  None
 * @retval None
 * @note   此函数应该在高频定时器中断中调用
 */
void DAC_UpdateSineOutput(void)
{
    if (!dac_output_enabled)
        return;

    // 更新相位
    current_phase += phase_increment;

    // 相位回绕
    if (current_phase >= DAC_SINE_SAMPLES)
    {
        current_phase -= DAC_SINE_SAMPLES;
    }

    // 获取当前索引
    sine_index = (uint32_t)current_phase;

    // 输出正弦波值
    DAC_SetChannel1Value(sine_table[sine_index]);
}

/**
 * @brief  设置DAC幅度倍数
 * @param  multiplier: 幅度倍数 (1.0-2.0)
 * @retval None
 */
void DAC_SetAmplitudeMultiplier(float multiplier)
{
//    // 限制倍数范围在1.0-2.0之间
//    if (multiplier < 1.0f)
//        multiplier = 1.0f;
//    if (multiplier > 2.0f)
//        multiplier = 2.0f;

    dac_amplitude_multiplier = multiplier;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  切换到下一个幅度倍数
 * @param  None
 * @retval None
 * @note   循环：1.0 -> 1.1 -> 1.2 -> ... -> 2.0 -> 1.0
 */
void DAC_NextAmplitudeMultiplier(void)
{
    // 增加0.1倍数
    dac_amplitude_multiplier += 0.1f;

    // 如果超过2.0，回到1.0
    if (dac_amplitude_multiplier > 2.05f)
        dac_amplitude_multiplier = 1.0f;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  获取当前DAC幅度倍数
 * @param  None
 * @retval 当前幅度倍数
 */
float DAC_GetAmplitudeMultiplier(void)
{
    return dac_amplitude_multiplier;
}

/**
 * @brief  设置DAC用户使能状态
 * @param  enable: 1-使能, 0-禁用
 * @retval None
 */
void DAC_SetUserEnable(uint8_t enable)
{
    dac_user_enabled = enable;

    if (enable)
    {
        // 用户使能时，根据当前频率决定是否启动输出
        DAC_StartSineOutput();
    }
    else
    {
        // 用户禁用时，停止输出
        DAC_StopSineOutput();
    }
}

/**
 * @brief  获取DAC用户使能状态
 * @param  None
 * @retval 用户使能状态
 */
uint8_t DAC_GetUserEnable(void)
{
    return dac_user_enabled;
}

/**
 * @brief  使能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Enable(void)
{
    DAC_Cmd(DAC_Channel_1, ENABLE);
}

/**
 * @brief  失能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Disable(void)
{
    DAC_Cmd(DAC_Channel_1, DISABLE);
}

/**
 * @brief  DAC测试函数 - 输出不同电压值进行测试
 * @param  None
 * @retval None
 * @note   可以用万用表测量PA4引脚的电压变化
 */
void DAC_Test(void)
{
    // 测试不同的电压输出
    DAC_SetChannel1Voltage(0.0f);   // 0V
    delay_ms(1000);

    DAC_SetChannel1Voltage(1.65f);  // 1.65V (中间值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(3.3f);   // 3.3V (最大值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(0.8f);   // 0.8V
    delay_ms(1000);

    DAC_SetChannel1Voltage(2.5f);   // 2.5V
    delay_ms(1000);

    // 回到0V
    DAC_SetChannel1Voltage(0.0f);
}
