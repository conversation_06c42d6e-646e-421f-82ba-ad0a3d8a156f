#ifndef __SYS_H
#define __SYS_H	
#include "stm32f4xx.h"
#include "stm32f4xx_gpio.h" // Include GPIO definitions

//////////////////////////////////////////////////////////////////////////////////	
// This program is for learning purposes only, and may not be used for other purposes
// without the author's permission.
// ALIENTEK STM32F407 Development Board
// System Clock Initialization
// Includes clock settings/interrupt management/GPIO settings etc.
// ALIENTEK @ ALIENTEK
// Technical Forum: www.openedv.com
// Creation Date: 2014/5/2
// Version: V1.0
// Copyright (C) Guangzhou Xingyi Electronic Technology Co., Ltd. 2014-2024
// All rights reserved
// ********************************************************************************
// Modification Notes
// None
////////////////////////////////////////////////////////////////////////////////// 


// 0, does not support ucos
// 1, supports ucos
#define SYSTEM_SUPPORT_OS		0		// Define whether the system folder supports UCOS

// Bit band operation, realizing GPIO control similar to 51
// For specific implementation ideas, refer to "CM3 Authoritative Guide" Chapter 5 (pages 87~92). M4 is similar to M3, but the register address has changed.
// IO port operation macro definition
#define BITBAND(addr, bitnum) ((addr & 0xF0000000)+0x2000000+((addr &0xFFFFF)<<5)+(bitnum<<2))
#define MEM_ADDR(addr)  *((volatile unsigned long  *)(addr))
#define BIT_ADDR(addr, bitnum)   MEM_ADDR(BITBAND(addr, bitnum))
// IO port address mapping
#define GPIOA_ODR_Addr    (GPIOA_BASE+20) //0x40020014
#define GPIOB_ODR_Addr    (GPIOB_BASE+20) //0x40020414
#define GPIOC_ODR_Addr    (GPIOC_BASE+20) //0x40020814
#define GPIOD_ODR_Addr    (GPIOD_BASE+20) //0x40020C14
#define GPIOE_ODR_Addr    (GPIOE_BASE+20) //0x40021014
#define GPIOF_ODR_Addr    (GPIOF_BASE+20) //0x40021414
#define GPIOG_ODR_Addr    (GPIOG_BASE+20) //0x40021814
#define GPIOH_ODR_Addr    (GPIOH_BASE+20) //0x40021C14
#define GPIOI_ODR_Addr    (GPIOI_BASE+20) //0x40022014

#define GPIOA_IDR_Addr    (GPIOA_BASE+16) //0x40020010
#define GPIOB_IDR_Addr    (GPIOB_BASE+16) //0x40020410
#define GPIOC_IDR_Addr    (GPIOC_BASE+16) //0x40020810
#define GPIOD_IDR_Addr    (GPIOD_BASE+16) //0x40020C10
#define GPIOE_IDR_Addr    (GPIOE_BASE+16) //0x40021010
#define GPIOF_IDR_Addr    (GPIOF_BASE+16) //0x40021410
#define GPIOG_IDR_Addr    (GPIOG_BASE+16) //0x40021810
#define GPIOH_IDR_Addr    (GPIOH_BASE+16) //0x40021C10
#define GPIOI_IDR_Addr    (GPIOI_BASE+16) //0x40022010

// IO port operation, only for a single IO port!
// Ensure that the value of n is less than 16!
#define PAout(n)   BIT_ADDR(GPIOA_ODR_Addr,n)  // Output
#define PAin(n)    BIT_ADDR(GPIOA_IDR_Addr,n)  // Input

#define PBout(n)   BIT_ADDR(GPIOB_ODR_Addr,n)  // Output
#define PBin(n)    BIT_ADDR(GPIOB_IDR_Addr,n)  // Input

#define PCout(n)   BIT_ADDR(GPIOC_ODR_Addr,n)  // Output
#define PCin(n)    BIT_ADDR(GPIOC_IDR_Addr,n)  // Input

#define PDout(n)   BIT_ADDR(GPIOD_ODR_Addr,n)  // Output
#define PDin(n)    BIT_ADDR(GPIOD_IDR_Addr,n)  // Input

#define PEout(n)   BIT_ADDR(GPIOE_ODR_Addr,n)  // Output
#define PEin(n)    BIT_ADDR(GPIOE_IDR_Addr,n)  // Input

#define PFout(n)   BIT_ADDR(GPIOF_ODR_Addr,n)  // Output
#define PFin(n)    BIT_ADDR(GPIOF_IDR_Addr,n)  // Input

#define PGout(n)   BIT_ADDR(GPIOG_ODR_Addr,n)  // Output
#define PGin(n)    BIT_ADDR(GPIOG_IDR_Addr,n)  // Input

#define PHout(n)   BIT_ADDR(GPIOH_ODR_Addr,n)  // Output
#define PHin(n)    BIT_ADDR(GPIOH_IDR_Addr,n)  // Input

#define PIout(n)   BIT_ADDR(GPIOI_ODR_Addr,n)  // Output
#define PIin(n)    BIT_ADDR(GPIOI_IDR_Addr,n)  // Input

// Define SYS_GPIO_PINx for each pin
#define SYS_GPIO_PIN0    GPIO_Pin_0
#define SYS_GPIO_PIN1    GPIO_Pin_1
#define SYS_GPIO_PIN2    GPIO_Pin_2
#define SYS_GPIO_PIN3    GPIO_Pin_3
#define SYS_GPIO_PIN4    GPIO_Pin_4
#define SYS_GPIO_PIN5    GPIO_Pin_5
#define SYS_GPIO_PIN6    GPIO_Pin_6
#define SYS_GPIO_PIN7    GPIO_Pin_7
#define SYS_GPIO_PIN8    GPIO_Pin_8
#define SYS_GPIO_PIN9    GPIO_Pin_9
#define SYS_GPIO_PIN10   GPIO_Pin_10
#define SYS_GPIO_PIN11   GPIO_Pin_11
#define SYS_GPIO_PIN12   GPIO_Pin_12
#define SYS_GPIO_PIN13   GPIO_Pin_13
#define SYS_GPIO_PIN14   GPIO_Pin_14
#define SYS_GPIO_PIN15   GPIO_Pin_15

// Define GPIO modes using standard peripheral library definitions
#define SYS_GPIO_MODE_IN    GPIO_Mode_IN
#define SYS_GPIO_MODE_OUT   GPIO_Mode_OUT
#define SYS_GPIO_MODE_AF    GPIO_Mode_AF
#define SYS_GPIO_MODE_AIN   GPIO_Mode_AN

// Define GPIO output types
#define SYS_GPIO_OTYPE_PP   GPIO_OType_PP
#define SYS_GPIO_OTYPE_OD   GPIO_OType_OD

// Define GPIO speeds
#define SYS_GPIO_SPEED_LOW  GPIO_Speed_2MHz
#define SYS_GPIO_SPEED_MID  GPIO_Speed_25MHz
#define SYS_GPIO_SPEED_FAST GPIO_Speed_50MHz
#define SYS_GPIO_SPEED_HIGH GPIO_Speed_100MHz

// Define GPIO pull-up/pull-down
#define SYS_GPIO_PUPD_NONE  GPIO_PuPd_NOPULL
#define SYS_GPIO_PUPD_PU    GPIO_PuPd_UP
#define SYS_GPIO_PUPD_PD    GPIO_PuPd_DOWN
#define SYS_GPIO_PUPD_RES   GPIO_PuPd_NOPULL // Reserved, mapping to no pull

// Trigger modes for external interrupts
#define SYS_GPIO_FTIR       0x01    // Falling edge trigger
#define SYS_GPIO_RTIR       0x02    // Rising edge trigger
#define SYS_GPIO_BTIR       0x03    // Both edges trigger

// Function prototypes for sys.c
void WFI_SET(void);             // Execute WFI instruction
void INTX_DISABLE(void);        // Disable all interrupts
void INTX_ENABLE(void);         // Enable all interrupts
void MSR_MSP(uint32_t addr);    // Set stack top address

// Function prototypes for GPIO configuration
void sys_nvic_set_vector_table(uint32_t baseaddr, uint32_t offset);
void sys_nvic_init(uint8_t pprio, uint8_t sprio, uint8_t ch, uint8_t group);
void sys_nvic_ex_config(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t tmode);
void sys_gpio_af_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t afx);
void sys_gpio_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint32_t mode, uint32_t otype, uint32_t ospeed, uint32_t pupd);
void sys_gpio_pin_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t status);
uint8_t sys_gpio_pin_get(GPIO_TypeDef *p_gpiox, uint16_t pinx);
void sys_wfi_set(void);
void sys_intx_disable(void);
void sys_intx_enable(void);
void sys_msr_msp(uint32_t addr);
void sys_standby(void);
void sys_soft_reset(void);
uint8_t sys_clock_set(uint32_t plln, uint32_t pllm, uint32_t pllp, uint32_t pllq);
void sys_stm32_clock_init(uint32_t plln, uint32_t pllm, uint32_t pllp, uint32_t pllq);

#endif
