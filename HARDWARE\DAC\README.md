# DAC 硬件模块 - 正弦波输出

## 概述
本模块实现了STM32F4的DAC功能，将PA4引脚配置为DAC输出（DAC_OUT1），可以输出与AD9833相同频率的正弦波信号。

## 引脚配置
- **PA4**: DAC通道1输出 (DAC_OUT1)
- **分辨率**: 12位 (0-4095)
- **输出电压范围**: 0V - 3.3V (动态调整)
- **偏移电压**: 1.65V (正弦波中心电压)
- **幅度**: 根据频率动态调整 (见频率-幅度映射表)

## 主要功能

### 初始化函数
```c
void DAC_PA4_Init(void);        // 初始化DAC硬件
void DAC_SineWave_Init(void);   // 初始化正弦波查找表
```

### 基本输出控制函数
```c
void DAC_SetChannel1Value(uint16_t value);    // 设置12位数字值 (0-4095)
void DAC_SetChannel1Voltage(float voltage);   // 设置输出电压 (0.0V-3.3V)
```

### 正弦波控制函数
```c
void DAC_SetSineFrequency(float frequency);   // 设置正弦波频率(自动调整幅度)
void DAC_StartSineOutput(void);               // 启动正弦波输出
void DAC_StopSineOutput(void);                // 停止正弦波输出
void DAC_UpdateSineOutput(void);              // 更新正弦波输出(定时器中断调用)
float DAC_GetAmplitudeForFrequency(float frequency);  // 根据频率获取幅度
```

### 使能控制函数
```c
void DAC_Enable(void);     // 使能DAC通道1
void DAC_Disable(void);    // 失能DAC通道1
```

## 正弦波输出特性
- **频率范围**: 与AD9833同步，最大3kHz
- **DAC电压范围**: 1.49V - 1.81V (小幅度变化)
- **中心电压**: 1.65V (固定)
- **波形**: 正弦波，256点查找表
- **幅度控制**: 根据频率反向计算所需DAC幅度
- **外部输出**: 经放大后恒定1V峰峰值
- **频率限制**: 超过3kHz时自动停止输出
- **采样率**: 100kHz (TIM6定时器中断)

## 频率-幅度映射表 (DAC输出)
| 频率(Hz) | 幅度因子 | DAC峰峰值(V) | DAC电压范围(V) | 外部输出(V) |
|----------|----------|--------------|----------------|-------------|
| 100      | 4.615    | 0.217        | 1.54-1.76      | 1.0         |
| 200      | 4.462    | 0.224        | 1.54-1.76      | 1.0         |
| 300      | 4.325    | 0.231        | 1.53-1.77      | 1.0         |
| 400      | 4.058    | 0.246        | 1.53-1.78      | 1.0         |
| 500      | 3.727    | 0.268        | 1.52-1.79      | 1.0         |
| 1000     | 2.475    | 0.404        | 1.45-1.85      | 1.0         |
| 1500     | 1.876    | 0.533        | 1.38-1.92      | 1.0         |
| 2000     | 1.324    | 0.755        | 1.27-2.03      | 1.0         |
| 2500     | 1.024    | 0.977        | 1.16-2.14      | 1.0         |
| 2900     | 0.8415   | 1.188        | 1.06-2.24      | 1.0         |
| 3000     | 0.825    | 1.212        | 1.04-2.26      | 1.0         |
| >3000    | 0        | 0            | 1.65V(停止)    | 0           |

**说明**: DAC输出经过外部电路放大，使得最终输出始终为1V峰峰值

## 使用示例

### 正弦波输出使用
```c
#include "dac.h"
#include "timer.h"

int main(void)
{
    // 初始化DAC硬件和正弦波功能
    DAC_PA4_Init();
    DAC_SineWave_Init();

    // 初始化TIM6用于正弦波输出 (100kHz中断)
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // 设置输出频率为1kHz正弦波
    DAC_SetSineFrequency(1000.0f);

    while(1)
    {
        // 主循环 - 正弦波在定时器中断中自动更新
        delay_ms(100);
    }
}
```

### 与AD9833同步使用
```c
// 在频率调整函数中同时设置AD9833和DAC
void adjust_frequency(float step) {
    current_frequency += step;

    // 设置AD9833频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 同时设置DAC正弦波频率 (小幅度输出，经外部放大为1V)
    DAC_SetSineFrequency(current_frequency);
}
```

### 电压计算公式
```
幅度因子 = amplitude_table[频率索引]  // 从映射表获取
DAC峰峰值 = (1V / 幅度因子) × 倍数  // 反向计算DAC所需峰峰值，乘以倍数
DAC幅度 = DAC峰峰值 / 2  // DAC输出幅度
正弦波电压 = 1.65V + DAC幅度 × sin(2πft)
外部输出 = DAC输出 × 幅度因子 = 1V × 倍数
DAC值 = (电压 / 3.3V) × 4095
```

## 技术实现细节
1. **正弦波生成**: 使用256点查找表，根据频率动态生成
2. **定时器配置**: TIM6产生100kHz中断，驱动DAC输出更新
3. **频率控制**: 通过相位增量控制正弦波频率
4. **幅度控制**: 30点频率-幅度映射表，支持线性插值
5. **电压范围**: DAC输出小幅度正弦波，中心电压1.65V
6. **外部放大**: DAC输出经外部电路放大，最终输出恒定1V峰峰值
6. **动态调整**: 频率改变时自动重新生成查找表

## 验证方法
1. **示波器测量**: 连接示波器到PA4引脚，观察正弦波输出
2. **频率验证**: 调整频率按钮，确认DAC输出频率与AD9833同步
3. **电压测量**: 使用万用表测量PA4的直流电压应为1.65V左右
4. **频率限制测试**: 将频率调整到3kHz以上，确认DAC停止输出

## 注意事项
1. PA4之前配置为ADC2输入，现在改为DAC输出
2. DAC输出缓冲器已使能，可以直接驱动外部负载
3. 使用示波器可以观察PA4引脚的正弦波输出
4. 当频率超过3kHz时，DAC自动停止输出并显示"DAC: OFF"
5. 相关的ADC2和DMA2功能已被注释掉
6. TIM6中断优先级设置为抢占优先级2，子优先级1

## 文件结构
- `dac.h`: DAC模块头文件
- `dac.c`: DAC模块实现文件
- `README.md`: 本说明文档
- `timer.c`: 包含TIM6_DAC_Init()和TIM6_DAC_IRQHandler()
- `timer.h`: 包含TIM6相关函数声明
