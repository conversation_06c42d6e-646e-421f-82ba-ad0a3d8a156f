#include "sys.h"
//////////////////////////////////////////////////////////////////////////////////
// ?????????,??????,????????
// ALIENTEK STM32F407 ???
// ???????
// ??????/????/GPIO ???
// ALIENTEK @ ALIENTEK
// ????: www.openedv.com
// ????: 2014/5/2
// ??: V1.0
// ???? (C) ????????????? 2014-2024
// ??????
// ********************************************************************************
// ????
// ?
//////////////////////////////////////////////////////////////////////////////////


// THUMB ?????????
// ???????? WFI ????
__asm void WFI_SET(void)
{
	WFI;
}
// ?????? (??????? NMI ??)
__asm void INTX_DISABLE(void)
{
	CPSID   I
	BX      LR
}
// ??????
__asm void INTX_ENABLE(void)
{
	CPSIE   I
	BX      LR
}
// ????????
// addr: ??????
__asm void MSR_MSP(u32 addr)
{
	MSR MSP, r0
	BX LR // ?? BX LR ???????
}

/**
 ****************************************************************************************************
 * @file        sys.c
 * <AUTHOR>
 * @version     V1.0
 * @date        2021-12-30
 * @brief       ??????? (??????/????/GPIO ???)
 * @license     Copyright (c) 2020-2032, Guangzhou Xingyi Electronic Technology Co., Ltd.
 ****************************************************************************************************
 * @attention
 *
 * ????: ALIENTEK STM32F407 ???
 * ????: www.yuanzige.com
 * ????: www.openedv.com
 * ????: www.alientek.com
 * ????: openedv.taobao.com
 *
 * ????
 * V1.0 20211230
 * ????
 ****************************************************************************************************
 */

#include "sys.h"


/**
 * @brief       ???????????
 * @param       baseaddr: ???
 * @param       offset: ??? (??? 0,? 0X100 ???)
 * @retval      ?
 */
void sys_nvic_set_vector_table(uint32_t baseaddr, uint32_t offset)
{
    /* ?? NVIC ?????????,VTOR ? 9 ???,? [8:0] ?? */
    SCB->VTOR = baseaddr | (offset & (uint32_t)0xFFFFFE00);
}

/**
 * @brief       ?? NVIC ??
 * @param       group: 0~4,? 5 ?,??????:sys_nvic_init ??????
 * @retval      ?
 */
static void sys_nvic_priority_group_config(uint8_t group)
{
    uint32_t temp, temp1;
    temp1 = (~group) & 0x07;/* ????? */
    temp1 <<= 8;
    temp = SCB->AIRCR;      /* ??????? */
    temp &= 0X0000F8FF;     /* ??????? */
    temp |= 0X05FA0000;     /* ??? */
    temp |= temp1;
    SCB->AIRCR = temp;      /* ???? */
}

/**
 * @brief       ?? NVIC (????/?????/?????)
 * @param       pprio: ?????
 * @param       sprio: ????
 * @param       ch: ????
 * @param       group: ????
 * @arg       0, ? 0: 0 ??????,4 ?????
 * @arg       1, ? 1: 1 ??????,3 ?????
 * @arg       2, ? 2: 2 ??????,2 ?????
 * @arg       3, ? 3: 3 ??????,1 ?????
 * @arg       4, ? 4: 4 ??????,0 ?????
 * @note        ????????????????!????????????
 * @retval      ?
 */
void sys_nvic_init(uint8_t pprio, uint8_t sprio, uint8_t ch, uint8_t group)
{
    uint32_t temp;
    sys_nvic_priority_group_config(group);  /* ???? */
    temp = pprio << (4 - group);
    temp |= sprio & (0x0f >> group);
    temp &= 0xf;                            /* ???? */
    NVIC->ISER[ch / 32] |= 1 << (ch % 32);  /* ????? (???,? ICER ?????? 1) */
    NVIC->IP[ch] |= temp << 4;              /* ????????????? */
}

/**
 * @brief       ????????,???? GPIOA~GPIOI
 * @note        ????????????????
 * @param       p_gpiox: GPIOA~GPIOG,GPIO ??
 * @param       pinx: 0X0000~0XFFFF,????,?????? IO,? 0 ?? Px0,? 1 ?? Px1,???????,0X0101 ?????? Px0 ? Px8?
 * @arg       SYS_GPIO_PIN0~SYS_GPIO_PIN15, 1<<0 ~ 1<<15
 * @param       tmode: 1~3,????
 * @arg       SYS_GPIO_FTIR, 1, ?????
 * @arg       SYS_GPIO_RTIR, 2, ?????
 * @arg       SYS_GPIO_BTIR, 3, ??????
 * @retval      ?
 */
void sys_nvic_ex_config(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t tmode)
{
    uint8_t offset;
    uint32_t gpio_num = 0;      /* gpio ?,0~10,?? GPIOA~GPIOG */
    uint32_t pinpos = 0, pos = 0, curpin = 0;

    gpio_num = ((uint32_t)p_gpiox - (uint32_t)GPIOA) / 0X400 ;/* ?? gpio ? */
    RCC->APB2ENR |= 1 << 14;    /* ?? SYSCFG ?? */

    for (pinpos = 0; pinpos < 16; pinpos++)
    {
        pos = 1 << pinpos;      /* ???? */
        curpin = pinx & pos;    /* ?????????? */

        if (curpin == pos)      /* ???? */
        {
            offset = (pinpos % 4) * 4;
            SYSCFG->EXTICR[pinpos / 4] &= ~(0x000F << offset);  /* ??????!!! */
            SYSCFG->EXTICR[pinpos / 4] |= gpio_num << offset;   /* EXTI.BITx ??? gpiox.bitx */

            EXTI->IMR |= 1 << pinpos;   /* ??? BITx ???? (?????,??????) */

            if (tmode & 0x01) EXTI->FTSR |= 1 << pinpos;        /* ? bitx ??????? */

            if (tmode & 0x02) EXTI->RTSR |= 1 << pinpos;        /* ? bitx ??????? */
        }
    }
}

/**
 * @brief       GPIO ????????
 * @param       p_gpiox: GPIOA~GPIOI,GPIO ??
 * @param       pinx: 0X0000~0XFFFF,????,?????? IO,? 0 ?? Px0,? 1 ?? Px1,???????,0X0101 ?????? Px0 ? Px8?
 * @arg       SYS_GPIO_PIN0~SYS_GPIO_PIN15, 1<<0 ~ 1<<15
 * @param       afx:0~15,?? AF0~AF15?
 * AF0~15 ?? (????????,????? STM32F407xx ????,? 7):
 * @arg       AF0:MCO/SWD/SWCLK/RTC       AF1:TIM1/TIM2               AF2:TIM3~5                  AF3:TIM8~11
 * @arg       AF4:I2C1~I2C3               AF5:SPI1/SPI2/I2S2          AF6:SPI3/I2S3               AF7:USART1~3
 * @arg       AF8:USART4~6                AF9;CAN1/CAN2/TIM12~14      AF10:USB_OTG/USB_HS         AF11:ETH
 * @arg       AF12:FSMC/SDIO/OTG_FS       AF13:DCIM                   AF14:                       AF15:EVENTOUT
 * @retval      ?
 */
void sys_gpio_af_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t afx)
{
    uint32_t pinpos = 0, pos = 0, curpin = 0;;

    for (pinpos = 0; pinpos < 16; pinpos++)
    {
        pos = 1 << pinpos;      /* ???? */
        curpin = pinx & pos;    /* ?????????? */

        if (curpin == pos)      /* ???? */
        {
            p_gpiox->AFR[pinpos >> 3] &= ~(0X0F << ((pinpos & 0X07) * 4));
            p_gpiox->AFR[pinpos >> 3] |= (uint32_t)afx << ((pinpos & 0X07) * 4);
        }
    }
}

/**
 * @brief       GPIO ????
 * @param       p_gpiox: GPIOA~GPIOI,GPIO ??
 * @param       pinx: 0X0000~0XFFFF,????,?????? IO,? 0 ?? Px0,? 1 ?? Px1,???????,0X0101 ?????? Px0 ? Px8?
 * @arg       SYS_GPIO_PIN0~SYS_GPIO_PIN15, 1<<0 ~ 1<<15
 *
 * @param       mode: 0~3; ????,????:
 * @arg       SYS_GPIO_MODE_IN,  0, ???? (????????)
 * @arg       SYS_GPIO_MODE_OUT, 1, ????
 * @arg       SYS_GPIO_MODE_AF,  2, ??????
 * @arg       SYS_GPIO_MODE_AIN, 3, ??????
 *
 * @param       otype: 0 / 1; ??????,????:
 * @arg       SYS_GPIO_OTYPE_PP, 0, ????
 * @arg       SYS_GPIO_OTYPE_OD, 1, ????
 *
 * @param       ospeed: 0~3; ????,????:
 * @arg       SYS_GPIO_SPEED_LOW,  0, ??
 * @arg       SYS_GPIO_SPEED_MID,  1, ??
 * @arg       SYS_GPIO_SPEED_FAST, 2, ??
 * @arg       SYS_GPIO_SPEED_HIGH, 3, ??
 *
 * @param       pupd: 0~3: ??/????,????:
 * @arg       SYS_GPIO_PUPD_NONE, 0, ???/??
 * @arg       SYS_GPIO_PUPD_PU,   1, ??
 * @arg       SYS_GPIO_PUPD_PD,   2, ??
 * @arg       SYS_GPIO_PUPD_RES,  3, ??
 *
 * @note:       ??:????? (????/????) ?,OTYPE ? OSPEED ????!!
 * @retval      ?
 */
void sys_gpio_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint32_t mode, uint32_t otype, uint32_t ospeed, uint32_t pupd)
{
    uint32_t pinpos = 0, pos = 0, curpin = 0;

    for (pinpos = 0; pinpos < 16; pinpos++)
    {
        pos = 1 << pinpos;      /* ???? */
        curpin = pinx & pos;    /* ?????????? */

        if (curpin == pos)      /* ???? */
        {
            p_gpiox->MODER &= ~(3 << (pinpos * 2)); /* ??????? */
            p_gpiox->MODER |= mode << (pinpos * 2); /* ????? */

            if ((mode == 0X01) || (mode == 0X02))   /* ???????/?????? */
            {
                p_gpiox->OSPEEDR &= ~(3 << (pinpos * 2));       /* ?????? */
                p_gpiox->OSPEEDR |= (ospeed << (pinpos * 2));   /* ?????? */
                p_gpiox->OTYPER &= ~(1 << pinpos) ;             /* ?????? */
                p_gpiox->OTYPER |= otype << pinpos;             /* ??????? */
            }

            p_gpiox->PUPDR &= ~(3 << (pinpos * 2)); /* ??????? */
            p_gpiox->PUPDR |= pupd << (pinpos * 2); /* ??????/?? */
        }
    }
}

/**
 * @brief       ?? GPIO ???????
 * @param       p_gpiox: GPIOA~GPIOI,GPIO ??
 * @param       pinx: 0X0000~0XFFFF,????,?????? IO,? 0 ?? Px0,? 1 ?? Px1,???????,0X0101 ?????? Px0 ? Px8?
 * @arg       SYS_GPIO_PIN0~SYS_GPIO_PIN15, 1<<0 ~ 1<<15
 * @param       status: 0/1,???? (??????),????:
 * @arg       0, ?????
 * @arg       1, ?????
 * @retval      ?
 */
void sys_gpio_pin_set(GPIO_TypeDef *p_gpiox, uint16_t pinx, uint8_t status)
{
    if (status & 0X01)
    {
        p_gpiox->BSRRL = pinx;  /* ? GPIOx ? pinx ??? 1 (?? BSRRL) */
    }
    else
    {
        p_gpiox->BSRRH = pinx;  /* ? GPIOx ? pinx ??? 0 (?? BSRRH) */
    }
}

/**
 * @brief       ?? GPIO ?????
 * @param       p_gpiox: GPIOA~GPIOG,GPIO ??
 * @param       pinx: 0X0000~0XFFFF,????,?????? IO,? 0 ?? Px0,? 1 ?? Px1,????????????? GPIO!
 * @arg       SYS_GPIO_PIN0~SYS_GPIO_PIN15, 1<<0 ~ 1<<15
 * @retval      ??????,0,???;1,???
 */
uint8_t sys_gpio_pin_get(GPIO_TypeDef *p_gpiox, uint16_t pinx)
{
    if (p_gpiox->IDR & pinx)
    {
        return 1;   /* pinx ??? 1 */
    }
    else
    {
        return 0;   /* pinx ??? 0 */
    }
}

/**
 * @brief       ??:WFI ?? (??????,???????,??????)
 * @param       ?
 * @retval      ?
 */
void sys_wfi_set(void)
{
    __ASM volatile("wfi");
}

/**
 * @brief       ?????? (??????? NMI ??)
 * @param       ?
 * @retval      ?
 */
void sys_intx_disable(void)
{
    __ASM volatile("cpsid i");
}

/**
 * @brief       ??????
 * @param       ?
 * @retval      ?
 */
void sys_intx_enable(void)
{
    __ASM volatile("cpsie i");
}

/**
 * @brief       ????????
 * @note        ????? X ? MDK ???,??????
 * @param       addr: ??????
 * @retval      ?
 */
void sys_msr_msp(uint32_t addr)
{
    __set_MSP(addr);    /* ???????? */
}

/**
 * @brief       ??????
 * @param       ?
 * @retval      ?
 */
void sys_standby(void)
{
    RCC->APB1ENR |= 1 << 28;    /* ?????? */
    PWR->CSR |= 1 << 8;         /* ?? WKUP ???? */
    PWR->CR |= 1 << 2;          /* ?? WKUP ?? */
    PWR->CR |= 1 << 1;          /* PDDS = 1,???????? (PDDS) */
    SCB->SCR |= 1 << 2;         /* ?? SLEEPDEEP ? (SYS->CTRL) */
    sys_wfi_set();              /* ?? WFI ??,?????? */
}

/**
 * @brief       ?????
 * @param       ?
 * @retval      ?
 */
void sys_soft_reset(void)
{
    SCB->AIRCR = 0X05FA0000 | (uint32_t)0x04;
}

/**
 * @brief       ??????
 * @param       plln: ? PLL ???? (PLL ??),??:64~432?
 * @param       pllm: ? PLL ??? PLL ???? (PLL ???),??:2~63?
 * @param       pllp: ? PLL P ???? (PLL ???),????????,??:2, 4, 6, 8?(?? 4 ??)
 * @param       pllq: ? PLL Q ???? (PLL ???),??:2~15?
 * @note
 *
 * Fvco: VCO ??
 * Fsys: ??????,??? PLL ? P ????????
 * Fq:   ? PLL ? Q ????????
 * Fs:   ? PLL ??????,??? HSI, HSE ?
 * Fvco = Fs * (plln / pllm);
 * Fsys = Fvco / pllp = Fs * (plln / (pllm * pllp));
 * Fq   = Fvco / pllq = Fs * (plln / (pllm * pllq));
 *
 * ?????? 8M ?,???:plln = 336, pllm = 8, pllp = 2, pllq = 7?
 * ??: Fvco = 8 * (336 / 8) = 336Mhz
 * Fsys = pll_p_ck = 336 / 2 = 168Mhz
 * Fq   = pll_q_ck = 336 / 7 = 48Mhz
 *
 * F407 ??????:
 * CPU ?? (HCLK) = pll_p_ck = 168Mhz
 * AHB1/2/3 (rcc_hclk1/2/3) = 168Mhz
 * APB1 (rcc_pclk1) = pll_p_ck / 4 = 42Mhz
 * APB1 (rcc_pclk2) = pll_p_ck / 2 = 84Mhz
 *
 * @retval      ????:0,??;1,HSE ??;2,PLL1 ??;3,PLL2 ??;4,??????;
 */
uint8_t sys_clock_set(uint32_t plln, uint32_t pllm, uint32_t pllp, uint32_t pllq)
{
    uint32_t retry = 0;
    uint8_t retval = 0;
    uint8_t swsval = 0;

    RCC->CR |= 1 << 16; /* HSEON = 1,?? HSE */

    while (((RCC->CR & (1 << 17)) == 0) && (retry < 0X7FFF))
    {
        retry++;        /* ?? HSE RDY */
    }

    if (retry == 0X7FFF)
    {
        retval = 1;     /* HSE ??? */
    }
    else
    {
        RCC->APB1ENR |= 1 << 28;                /* ???????? */
        PWR->CR |= 3 << 14;                     /* ?????,???? 168Mhz */

        RCC->PLLCFGR |= 0X3F & pllm;            /* ??? PLL ?????,PLLM[5:0]: 2~63 */
        RCC->PLLCFGR |= plln << 6;              /* ??? PLL ????,PLLN[8:0]: 192~432 */
        RCC->PLLCFGR |= ((pllp >> 1) - 1) << 16;/* ??? PLL P ????,PLLP[1:0]: 0~3,?? 2~8 ?? */
        RCC->PLLCFGR |= pllq << 24;             /* ??? PLL Q ????,PLLQ[3:0]: 2~15 */
        RCC->PLLCFGR |= 1 << 22;                /* ??? PLL ????? HSE */

        RCC->CFGR |= 0 << 4;                    /* HPRE[3:0]  = 0, AHB  ???,rcc_hclk1/2/3 = pll_p_ck */
        RCC->CFGR |= 5 << 10;                   /* PPRE1[2:0] = 5, APB1 4 ??   rcc_pclk1 = pll_p_ck / 4 */
        RCC->CFGR |= 4 << 13;                   /* PPRE2[2:0] = 4, APB2 2 ??   rcc_pclk2 = pll_p_ck / 2 */

        RCC->CR |= 1 << 24;                     /* ??? PLL */

        retry = 0;
        while ((RCC->CR & (1 << 25)) == 0)      /* ?? PLL ?? */
        {
            retry++;

            if (retry > 0X1FFFFF)
            {
                retval = 2;                     /* ? PLL ??? */
                break;
            }
        }

        FLASH->ACR |= 1 << 8;                   /* ?????? */
        FLASH->ACR |= 1 << 9;                   /* ?????? */
        FLASH->ACR |= 1 << 10;                  /* ?????? */
        FLASH->ACR |= 5 << 0;                   /* 5 ? CPU ???? */

        RCC->CFGR |= 2 << 0;                    /* ??? PLL ?????? */

        retry = 0;
        while (swsval != 3)                     /* ???????????? pll_p_ck */
        {
            swsval = (RCC->CFGR & 0X0C) >> 2;   /* ?? SWS[1:0] ??,???????? */
            retry++;

            if (retry > 0X1FFFFF)
            {
                retval = 4; /* ?????? */
                break;
            }
        }
    }

    return retval;
}

/**
 * @brief       ?????????
 * @param       plln: PLL1 ???? (PLL ??),??:4~512?
 * @param       pllm: PLL1 ????? (PLL ???),??:2~63?
 * @param       pllp: PLL1 P ???? (PLL ???),????????,??:2~128?(??? 2 ???)
 * @param       pllq: PLL1 Q ???? (PLL ???),??:1~128?
 * @retval      ?
 */
void sys_stm32_clock_init(uint32_t plln, uint32_t pllm, uint32_t pllp, uint32_t pllq)
{
    RCC->CR = 0x00000001;           /* ?? HISON,?????? RC ???,????? */
    RCC->CFGR = 0x00000000;         /* ?? CFGR */
    RCC->PLLCFGR = 0x00000000;      /* ?? PLLCFGR */
    RCC->CIR = 0x00000000;          /* ?? CIR */

    sys_clock_set(plln, pllm, pllp, pllq);  /* ???? */

    /* ???????? */
#ifdef  VECT_TAB_RAM
    sys_nvic_set_vector_table(SRAM_BASE, 0x0);
#else
    sys_nvic_set_vector_table(FLASH_BASE, 0x0);
#endif
}
