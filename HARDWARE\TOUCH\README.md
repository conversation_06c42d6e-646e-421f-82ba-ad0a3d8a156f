# 正点原子4.3寸TFT LCD触屏驱动说明

## 硬件连接

### XPT2046触屏控制器连接：
```
STM32F407引脚    →    XPT2046引脚    功能
PB2             →    MISO          数据输入
PF11            →    MOSI          数据输出  
PB0             →    CLK           时钟信号
PC13            →    CS            片选信号
PB1             →    PEN           触摸检测（中断）
```

### 电源连接：
```
3.3V → XPT2046 VCC
GND  → XPT2046 GND
```

## 软件使用

### 1. 初始化
```c
#include "touch.h"

// 在main函数中初始化
tp_dev.init();  // 初始化触屏
```

### 2. 触屏校准
```c
// 开机时按住PE3键进行触屏校准
if (KEY1 == 0) {
    tp_dev.adjust();  // 触屏校准
}
```

### 3. 触屏扫描
```c
// 在主循环中扫描触屏
tp_dev.scan(0);  // 扫描触屏，0表示屏幕坐标

if (tp_dev.sta & TP_PRES_DOWN) {  // 触屏被按下
    uint16_t x = tp_dev.x;  // 获取X坐标
    uint16_t y = tp_dev.y;  // 获取Y坐标
    
    // 处理触摸事件
    // ...
}
```

## 当前项目中的触屏功能

### 虚拟按钮控制：
- **+100kHz按钮**：频率增加100kHz（蓝色）
- **+10kHz按钮**：频率增加10kHz（绿色）
- **+1kHz按钮**：频率增加1kHz（橙色）
- **+100Hz按钮**：频率增加100Hz（红色）

### 触屏区域控制：
- **按钮下方左半屏**：频率减少100Hz
- **按钮下方右半屏**：频率增加100Hz

### 物理按键控制：
- **PE4**：频率增加100Hz
- **PE3**：频率增加100kHz
- **开机按住PE3**：进入触屏校准模式

### 显示信息：
- 当前频率大字体居中显示（自动单位转换）
- 四个虚拟按钮显示在频率下方
- 操作提示显示在屏幕下方
- 按钮点击有视觉反馈效果

### 频率范围：
- **最小频率**：100Hz
- **最大频率**：1.2MHz
- **超限处理**：超过1.2MHz后自动回到100Hz

## 界面布局

```
┌─────────────────────────────────────┐
│           AD9833 Frequency:         │
│                                     │
│            1.23 MHz                 │  ← 当前频率大字体显示
│                                     │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐
│  │+100kHz  │ │ +10kHz  │ │ +1kHz   │ │ +100Hz  │  ← 四个大尺寸按钮
│  │ (蓝色)  │ │ (绿色)  │ │ (橙色)  │ │ (红色)  │  ← 75x50像素，便于触摸
│  │         │ │         │ │         │ │         │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘
│                                     │
│     Touch buttons to adjust freq    │
│  Touch below: Left(-100Hz) Right(+100Hz) │  ← 操作提示
│     PE3: +100kHz  PE4: +100Hz      │
│     Hold PE3 at boot: Calibrate     │
└─────────────────────────────────────┘
```

## 操作方式

### 1. 虚拟按钮操作：
- **按钮尺寸**：75x50像素，大尺寸便于触摸操作
- **按钮字体**：16号字体，清晰易读
- 点击对应按钮实现不同幅度的频率增加
- 按钮有按下反馈效果（颜色反转）
- 适合快速调节到目标频率

### 2. 触屏区域操作：
- 在按钮下方区域触摸
- 左半屏减少100Hz，右半屏增加100Hz
- 适合精细调节

### 3. 物理按键操作：
- PE4：+100Hz（精细调节）
- PE3：+100kHz（快速调节）

## 注意事项

1. **首次使用需要校准**：开机时按住PE3键进行校准
2. **校准步骤**：依次点击屏幕四个角的红色十字标记
3. **校准精度**：校准时请准确点击十字中心
4. **触摸响应**：触摸后会有100ms延时防止频繁触发

## 故障排除

1. **触屏无响应**：
   - 检查硬件连接
   - 重新进行触屏校准

2. **坐标不准确**：
   - 重新校准触屏
   - 检查校准参数是否正确

3. **触摸漂移**：
   - 检查电源稳定性
   - 重新校准触屏
