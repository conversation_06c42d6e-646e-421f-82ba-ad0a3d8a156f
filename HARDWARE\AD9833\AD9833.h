#ifndef __AD9833_H
#define __AD9833_H

#include "stm32f4xx.h"
#include <stdbool.h>

// GPIO Pin Definitions
#define AD9833_SCLK_H()    GPIO_SetBits(GPIOA, GPIO_Pin_6)
#define AD9833_SCLK_L()    GPIO_ResetBits(GPIOA, GPIO_Pin_6)
#define AD9833_SDATA_H()   GPIO_SetBits(GPIOA, GPIO_Pin_7)
#define AD9833_SDATA_L()   GPIO_ResetBits(GPIOA, GPIO_Pin_7)
#define AD9833_CS1_H()     GPIO_SetBits(GPIOA, GPIO_Pin_5)
#define AD9833_CS1_L()     GPIO_ResetBits(GPIOA, GPIO_Pin_5)
#define AD9833_CS2_H()     GPIO_SetBits(GPIOF, GPIO_Pin_0)
#define AD9833_CS2_L()     GPIO_ResetBits(GPIOF, GPIO_Pin_0)

// Command Register Bits
#define AD9833_B28        (1 << 13)
#define AD9833_HLB        (1 << 12)
#define AD9833_FSEL0      (1 << 11)
#define AD9833_FSEL1      (1 << 11)
#define AD9833_PSEL0      (1 << 10)
#define AD9833_PSEL1      (1 << 10)
#define AD9833_RESET      (1 << 8)
#define AD9833_SLEEP1     (1 << 7)
#define AD9833_SLEEP12    (1 << 6)
#define AD9833_OPBITEN    (1 << 5)
#define AD9833_DIV2       (1 << 3)
#define AD9833_MODE       (1 << 1)

// Waveform Output Types
#define AD9833_OUT_SINUS     0x2000
#define AD9833_OUT_TRIANGLE  0x2002
#define AD9833_OUT_MSB       0x2028
#define AD9833_OUT_SINUS1    0x2000  // Channel 2
#define AD9833_OUT_TRIANGLE1 0x2002  // Channel 2
#define AD9833_OUT_MSB1      0x2028  // Channel 2

#define AD9833_B281 (1 << 13)  // Channel 2 B28 control

// Register Addresses
#define AD9833_REG_CMD    0x0000
#define AD9833_REG_FREQ0  0x4000
#define AD9833_REG_FREQ1  0x8000
#define AD9833_REG_PHASE0 0xC000
#define AD9833_REG_PHASE1 0xE000

// For AD9833_1 (Channel B)
#define AD9833_REG1_CMD    0x0000
#define AD9833_REG1_FREQ0  0x4000
#define AD9833_REG1_FREQ1  0x8000
#define AD9833_REG1_PHASE0 0xC000
#define AD9833_REG1_PHASE1 0xE000
#define AD9833_RESET1      0x0100

// Function Prototypes
void AD983_GPIO_Init(void);
void AD9833_Init(void);
unsigned char AD9833_SPI_Write(unsigned char* data, unsigned char bytesNumber, unsigned char channel);
void AD9833_SetRegisterValue(unsigned short regValue, unsigned char channel);
void AD9833_SetFrequencyQuick(float fout,unsigned short type);
void AD9833_SetFrequency(unsigned short reg, float fout,unsigned short type);
void AD9833_SetPhase(unsigned short reg, unsigned short val);
void AD9833_Setup(unsigned short freq, unsigned short phase,unsigned short type);
void AD9833_SetWave(unsigned short type);
void AD9833_Reset(void);
void AD9833_ClearReset(void);

// Channel B Functions
void AD983_GPIO_Init1(void);
void AD9833_Init1(void);
void AD9833_SetRegisterValue1(unsigned short regValue);
void AD9833_SetFrequencyQuick1(float fout,unsigned short type);
void AD9833_SetFrequency1(unsigned short reg, float fout,unsigned short type);
void AD9833_SetPhase1(unsigned short reg, unsigned short val);
void AD9833_Setup1(unsigned short freq, unsigned short phase,unsigned short type);
void AD9833_SetWave1(unsigned short type);
void AD9833_Reset1(void);
void AD9833_ClearReset1(void);

#endif
