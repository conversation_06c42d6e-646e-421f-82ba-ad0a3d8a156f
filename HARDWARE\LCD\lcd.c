/**
 ****************************************************************************************************
 * @file        lcd.c
 * <AUTHOR>
 * @version     V1.1
 * @date        2023-05-29
 * @brief       2.8 inch/3.5 inch/4.3 inch/7 inch TFTLCD (MCU screen) driver code
 * Supports driver IC models including: ILI9341/NT35310/NT35510/SSD1963/ST7789/ST7796/ILI9806 etc.
 *
 * @license     Copyright (c) 2022-2032, Guangzhou Xingyi Electronic Technology Co., Ltd.
 ****************************************************************************************************
 * @attention
 *
 * Experimental Platform: ALIENTEK STM32F407 Development Board
 * Online Video: www.yuanzige.com
 * Technical Forum: www.openedv.com
 * Company Website: www.alientek.com
 * Purchase Address: openedv.taobao.com
 *
 * Modification Notes
 * V1.0 20221127
 * First release
 * V1.1 20230529
 * 1. Added support for ST7796 and ILI9806 ICs
 * 2. Simplified some code to avoid long judgments
 ****************************************************************************************************
 */

#include "stdlib.h"
#include "lcd.h"
#include "lcdfont.h"
#include "usart.h"
#include "sys.h"          // ?? sys.h ??????? GPIO ??? (??, SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP ?)
                          // ??? sys_gpio_set ? sys_gpio_af_set ??
#include "lcd_ex.h"       // ?? lcd_ex.h ??? lcd_ex_..._reginit ??
#include "delay.h"        // ?? delay.h ??? delay_ms ??

/*
 * lcd_ex.c stores the register initialization code for each LCD driver IC to simplify lcd.c. This .c file
 * is not directly added to the project, only lcd.c will use it, so it is added via include. (Do not
 * include this .c file in other files!! Otherwise, it will report an error!)
 *
 * IMPORTANT: The line below is commented out. Instead of directly including the .c file,
 * we now include its corresponding header file (lcd_ex.h) which contains function prototypes.
 * This prevents "multiply defined" linker errors.
 */
// #include "lcd_ex.c"


/* LCD pen color and background color */
uint32_t g_point_color = 0XF800;    /* Pen color */
uint32_t g_back_color  = 0XFFFF;    /* Background color */

/* Manage important LCD parameters */
_lcd_dev lcddev;


/**
 * @brief       LCD write data
 * @param       data: Data to be written
 * @retval      None
 */
void lcd_wr_data(volatile uint16_t data)
{
    data = data;            /* Must insert delay when using -O2 optimization */
    LCD->LCD_RAM = data;
}

/**
 * @brief       LCD write register number/address function
 * @param       regno: Register number/address
 * @retval      None
 */
void lcd_wr_regno(volatile uint16_t regno)
{
    regno = regno;          /* Must insert delay when using -O2 optimization */
    LCD->LCD_REG = regno;   /* Write the register number to be written */
}

/**
 * @brief       LCD write register
 * @param       regno: Register number/address
 * @param       data: Data to be written
 * @retval      None
 */
void lcd_write_reg(uint16_t regno, uint16_t data)
{
    LCD->LCD_REG = regno;   /* Write the register number to be written */
    LCD->LCD_RAM = data;    /* Write data */
}

/**
 * @brief       LCD delay function, only used for some places that need to be set when mdk -O1 time optimization
 * @param       t: Delay value
 * @retval      None
 */
static void lcd_opt_delay(uint32_t i)
{
    while (i--);
}

/**
 * @brief       LCD read data
 * @param       None
 * @retval      Read data
 */
static uint16_t lcd_rd_data(void)
{
    volatile uint16_t ram;  /* Prevent optimization */
    lcd_opt_delay(2);
    ram = LCD->LCD_RAM;
    return ram;
}

/**
 * @brief       Prepare to write GRAM
 * @param       None
 * @retval      None
 */
void lcd_write_ram_prepare(void)
{
    LCD->LCD_REG = lcddev.wramcmd;
}

/**
 * @brief       Read the color value of a certain point
 * @param       x,y: Coordinates
 * @retval      Color of this point (32-bit color, compatible with LTDC)
 */
uint32_t lcd_read_point(uint16_t x, uint16_t y)
{
    uint16_t r = 0, g = 0, b = 0;

    if (x >= lcddev.width || y >= lcddev.height) return 0;  /* Out of range, return directly */

    lcd_set_cursor(x, y);       /* Set coordinates */

    if (lcddev.id == 0X5510)
    {
        lcd_wr_regno(0X2E00);   /* 5510 send read GRAM command */
    }
    else
    {
        lcd_wr_regno(0X2E);     /* 9341/5310/1963/7789/7796/9806 etc. send read GRAM command */
    }

    r = lcd_rd_data();          /* Dummy read */

    if (lcddev.id == 0X1963) return r;  /* 1963 can be read directly */

    r = lcd_rd_data();          /* Actual coordinate color */

    if (lcddev.id == 0X7796) return r;  /* 7796 reads one pixel value at a time */

    /* 9341/5310/5510/7789/9806 need to be read in 2 times */
    b = lcd_rd_data();
    g = r & 0XFF;               /* For 9341/5310/5510/7789/9806, the first read is RG value, R is first, G is second, each occupies 8 bits */
    g <<= 8;

    return (((r >> 11) << 11) | ((g >> 10) << 5) | (b >> 11));  /* 9341/5310/5510/7789/9806 needs formula conversion */
}

/**
 * @brief       LCD display on
 * @param       None
 * @retval      None
 */
void lcd_display_on(void)
{
    if (lcddev.id == 0X5510)
    {
        lcd_wr_regno(0X2900);   /* Turn on display */
    }
    else    /* 9341/5310/1963/7789/7796/9806 etc. send display on command */
    {
        lcd_wr_regno(0X29);     /* Turn on display */
    }
}

/**
 * @brief       LCD display off
 * @param       None
 * @retval      None
 */
void lcd_display_off(void)
{
    if (lcddev.id == 0X5510)
    {
        lcd_wr_regno(0X2800);   /* Turn off display */
    }
    else    /* 9341/5310/1963/7789/7796/9806 etc. send display off command */
    {
        lcd_wr_regno(0X28);     /* Turn off display */
    }
}

/**
 * @brief       Set cursor position (invalid for RGB screen)
 * @param       x,y: Coordinates
 * @retval      None
 */
void lcd_set_cursor(uint16_t x, uint16_t y)
{
    if (lcddev.id == 0X1963)
    {
        if (lcddev.dir == 0)    /* Portrait mode, x coordinate needs to be transformed */
        {
            x = lcddev.width - 1 - x;
            lcd_wr_regno(lcddev.setxcmd);
            lcd_wr_data(0);
            lcd_wr_data(0);
            lcd_wr_data(x >> 8);
            lcd_wr_data(x & 0XFF);
        }
        else                    /* Landscape mode */
        {
            lcd_wr_regno(lcddev.setxcmd);
            lcd_wr_data(x >> 8);
            lcd_wr_data(x & 0XFF);
            lcd_wr_data((lcddev.width - 1) >> 8);
            lcd_wr_data((lcddev.width - 1) & 0XFF);
        }

        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(y >> 8);
        lcd_wr_data(y & 0XFF);
        lcd_wr_data((lcddev.height - 1) >> 8);
        lcd_wr_data((lcddev.height - 1) & 0XFF);
    }
    else if (lcddev.id == 0X5510)
    {
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(x >> 8);
        lcd_wr_regno(lcddev.setxcmd + 1);
        lcd_wr_data(x & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(y >> 8);
        lcd_wr_regno(lcddev.setycmd + 1);
        lcd_wr_data(y & 0XFF);
    }
    else    /* 9341/5310/7789/7796/9806 etc. set coordinates */
    {
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(x >> 8);
        lcd_wr_data(x & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(y >> 8);
        lcd_wr_data(y & 0XFF);
    }
}

/**
 * @brief       Set LCD automatic scan direction (invalid for RGB screen)
 * @note
 * 9341/5310/5510/1963/7789/7796/9806 and other ICs have been actually tested
 * Note: Other functions may be affected by this function's settings (especially 9341),
 * so, generally set to L2R_U2D, if set to other scanning methods, it may cause abnormal display.
 *
 * @param       dir:0~7, represents 8 directions (see lcd.h for specific definitions)
 * @retval      None
 */
void lcd_scan_dir(uint8_t dir)
{
    uint16_t regval = 0;
    uint16_t dirreg = 0;
    uint16_t temp;

    /* For landscape screen, 1963 does not change scan direction, other ICs change scan direction! For portrait screen, 1963 changes direction, other ICs do not change scan direction */
    if ((lcddev.dir == 1 && lcddev.id != 0X1963) || (lcddev.dir == 0 && lcddev.id == 0X1963))
    {
        switch (dir)   /* Direction conversion */
        {
            case 0:
                dir = 6;
                break;

            case 1:
                dir = 7;
                break;

            case 2:
                dir = 4;
                break;

            case 3:
                dir = 5;
                break;

            case 4:
                dir = 1;
                break;

            case 5:
                dir = 0;
                break;

            case 6:
                dir = 3;
                break;

            case 7:
                dir = 2;
                break;
        }
    }

    /* Set the value of bits 5,6,7 of 0X36/0X3600 register according to the scanning method */
    switch (dir)
    {
        case L2R_U2D:   /* Left to right, top to bottom */
            regval |= (0 << 7) | (0 << 6) | (0 << 5);
            break;

        case L2R_D2U:   /* Left to right, bottom to top */
            regval |= (1 << 7) | (0 << 6) | (0 << 5);
            break;

        case R2L_U2D:   /* Right to left, top to bottom */
            regval |= (0 << 7) | (1 << 6) | (0 << 5);
            break;

        case R2L_D2U:   /* Right to left, bottom to top */
            regval |= (1 << 7) | (1 << 6) | (0 << 5);
            break;

        case U2D_L2R:   /* Top to bottom, left to right */
            regval |= (0 << 7) | (0 << 6) | (1 << 5);
            break;

        case U2D_R2L:   /* Top to bottom, right to left */
            regval |= (0 << 7) | (1 << 6) | (1 << 5);
            break;

        case D2U_L2R:   /* Bottom to top, left to right */
            regval |= (1 << 7) | (0 << 6) | (1 << 5);
            break;

        case D2U_R2L:   /* Bottom to top, right to left */
            regval |= (1 << 7) | (1 << 6) | (1 << 5);
            break;
    }

    dirreg = 0X36;  /* For most driver ICs, controlled by 0X36 register */

    if (lcddev.id == 0X5510)
    {
        dirreg = 0X3600;    /* For 5510, register is different from other driver ICs */
    }

    /* 9341 & 7789 & 7796 need to set BGR bit */
    if (lcddev.id == 0X9341 || lcddev.id == 0X7789 || lcddev.id == 0X7796)
    {
        regval |= 0X08;
    }

    lcd_write_reg(dirreg, regval);

    if (lcddev.id != 0X1963)   /* 1963 does not handle coordinates */
    {
        if (regval & 0X20)
        {
            if (lcddev.width < lcddev.height)   /* Swap X,Y */
            {
                temp = lcddev.width;
                lcddev.width = lcddev.height;
                lcddev.height = temp;
            }
        }
        else
        {
            if (lcddev.width > lcddev.height)   /* Swap X,Y */
            {
                temp = lcddev.width;
                lcddev.width = lcddev.height;
                lcddev.height = temp;
            }
        }
    }

    /* Set display area (window) size */
    if (lcddev.id == 0X5510)
    {
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(0);
        lcd_wr_regno(lcddev.setxcmd + 1);
        lcd_wr_data(0);
        lcd_wr_regno(lcddev.setxcmd + 2);
        lcd_wr_data((lcddev.width - 1) >> 8);
        lcd_wr_regno(lcddev.setxcmd + 3);
        lcd_wr_data((lcddev.width - 1) & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(0);
        lcd_wr_regno(lcddev.setycmd + 1);
        lcd_wr_data(0);
        lcd_wr_regno(lcddev.setycmd + 2);
        lcd_wr_data((lcddev.height - 1) >> 8);
        lcd_wr_regno(lcddev.setycmd + 3);
        lcd_wr_data((lcddev.height - 1) & 0XFF);
    }
    else
    {
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(0);
        lcd_wr_data(0);
        lcd_wr_data((lcddev.width - 1) >> 8);
        lcd_wr_data((lcddev.width - 1) & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(0);
        lcd_wr_data(0);
        lcd_wr_data((lcddev.height - 1) >> 8);
        lcd_wr_data((lcddev.height - 1) & 0XFF);
    }
}

/**
 * @brief       Draw point
 * @param       x,y: Coordinates
 * @param       color: Color of the point (32-bit color, compatible with LTDC)
 * @retval      None
 */
void lcd_draw_point(uint16_t x, uint16_t y, uint32_t color)
{
    lcd_set_cursor(x, y);       /* Set cursor position */
    lcd_write_ram_prepare();    /* Start writing GRAM */
    LCD->LCD_RAM = color;
}

/**
 * @brief       SSD1963 backlight brightness setting function
 * @param       pwm: Backlight level, 0~100. Larger value means brighter.
 * @retval      None
 */
void lcd_ssd_backlight_set(uint8_t pwm)
{
    lcd_wr_regno(0xBE);         /* Configure PWM output */
    lcd_wr_data(0x05);          /* 1 Set PWM frequency */
    lcd_wr_data(pwm * 2.55);    /* 2 Set PWM duty cycle */
    lcd_wr_data(0x01);          /* 3 Set C */
    lcd_wr_data(0xFF);          /* 4 Set D */
    lcd_wr_data(0x00);          /* 5 Set E */
    lcd_wr_data(0x00);          /* 6 Set F */
}

/**
 * @brief       Set LCD display direction
 * @param       dir:0, portrait; 1, landscape
 * @retval      None
 */
void lcd_display_dir(uint8_t dir)
{
    lcddev.dir = dir;   /* Portrait/Landscape */

    if (dir == 0)       /* Portrait */
    {
        lcddev.width = 240;
        lcddev.height = 320;

        if (lcddev.id == 0x5510)
        {
            lcddev.wramcmd = 0X2C00;
            lcddev.setxcmd = 0X2A00;
            lcddev.setycmd = 0X2B00;
            lcddev.width = 480;
            lcddev.height = 800;
        }
        else if (lcddev.id == 0X1963)
        {
            lcddev.wramcmd = 0X2C;  /* Set command to write GRAM */
            lcddev.setxcmd = 0X2B;  /* Set command to write X coordinate */
            lcddev.setycmd = 0X2A;  /* Set command to write Y coordinate */
            lcddev.width = 480;     /* Set width 480 */
            lcddev.height = 800;    /* Set height 800 */
        }
        else   /* Other ICs, including: 9341/5310/7789/7796/9806 etc. ICs */
        {
            lcddev.wramcmd = 0X2C;
            lcddev.setxcmd = 0X2A;
            lcddev.setycmd = 0X2B;
        }

        if (lcddev.id == 0X5310 || lcddev.id == 0X7796)    /* If it is 5310/7796, it means 320*480 resolution */
        {
            lcddev.width = 320;
            lcddev.height = 480;
        }

        if (lcddev.id == 0X9806)    /* If it is 9806, it means 480*800 resolution */
        {
            lcddev.width = 480;
            lcddev.height = 800;
        }
    }
    else                /* Landscape */
    {
        lcddev.width = 320;         /* Default width */
        lcddev.height = 240;        /* Default height */

        if (lcddev.id == 0x5510)
        {
            lcddev.wramcmd = 0X2C00;
            lcddev.setxcmd = 0X2A00;
            lcddev.setycmd = 0X2B00;
            lcddev.width = 800;
            lcddev.height = 480;
        }
        else if (lcddev.id == 0X1963 || lcddev.id == 0X9806)
        {
            lcddev.wramcmd = 0X2C;  /* Set command to write GRAM */
            lcddev.setxcmd = 0X2A;  /* Set command to write X coordinate */
            lcddev.setycmd = 0X2B;  /* Set command to write Y coordinate */
            lcddev.width = 800;     /* Set width 800 */
            lcddev.height = 480;    /* Set height 480 */
        }
        else   /* Other ICs, including: 9341/5310/7789/7796 etc. ICs */
        {
            lcddev.wramcmd = 0X2C;
            lcddev.setxcmd = 0X2A;
            lcddev.setycmd = 0X2B;
        }

        if (lcddev.id == 0X5310 || lcddev.id == 0X7796)    /* If it is 5310/7796, it means 320*480 resolution */
        {
            lcddev.width = 480;
            lcddev.height = 320;
        }
    }

    lcd_scan_dir(DFT_SCAN_DIR);     /* Default scan direction */
    LCD_BL(1);                      /* Turn on backlight */
}

/**
 * @brief       Set window (invalid for RGB screen), and automatically set drawing point coordinates to the top-left corner of the window (sx,sy).
 * @param       sx,sy: Window start coordinates (top-left corner)
 * @param       width,height: Window width and height, must be greater than 0!!
 * @note        Window size: width*height.
 *
 * @retval      None
 */
void lcd_set_window(uint16_t sx, uint16_t sy, uint16_t width, uint16_t height)
{
    uint16_t twidth, theight;
    twidth = sx + width - 1;
    theight = sy + height - 1;

    if (lcddev.id == 0X1963 && lcddev.dir != 1)    /* 1963 portrait special handling */
    {
        sx = lcddev.width - width - sx; // Corrected: Use sx instead of x
        theight = sy + height - 1; // Corrected: Use theight instead of height
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(0);
        lcd_wr_data(0);
        lcd_wr_data(sx >> 8); // Corrected: Use sx instead of x
        lcd_wr_data(sx & 0XFF); // Corrected: Use sx instead of x
        lcd_wr_data((sx + width - 1) >> 8);
        lcd_wr_data((sx + width - 1) & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(sy >> 8);
        lcd_wr_data(sy & 0XFF);
        lcd_wr_data(theight >> 8); // Corrected: Use theight instead of height
        lcd_wr_data(theight & 0XFF); // Corrected: Use theight instead of height
    }
    else if (lcddev.id == 0X5510)
    {
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(sx >> 8);
        lcd_wr_regno(lcddev.setxcmd + 1);
        lcd_wr_data(sx & 0XFF);
        lcd_wr_regno(lcddev.setxcmd + 2);
        lcd_wr_data(twidth >> 8);
        lcd_wr_regno(lcddev.setxcmd + 3);
        lcd_wr_data(twidth & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(sy >> 8);
        lcd_wr_regno(lcddev.setycmd + 1);
        lcd_wr_data(sy & 0XFF);
        lcd_wr_regno(lcddev.setycmd + 2);
        lcd_wr_data(theight >> 8);
        lcd_wr_regno(lcddev.setycmd + 3);
        lcd_wr_data(theight & 0XFF);
    }
    else    /* 9341/5310/7789/1963/7796/9806 landscape etc. set window */
    {
        lcd_wr_regno(lcddev.setxcmd);
        lcd_wr_data(sx >> 8);
        lcd_wr_data(sx & 0XFF);
        lcd_wr_data(twidth >> 8);
        lcd_wr_data(twidth & 0XFF);
        lcd_wr_regno(lcddev.setycmd);
        lcd_wr_data(sy >> 8);
        lcd_wr_data(sy & 0XFF);
        lcd_wr_data(theight >> 8);
        lcd_wr_data(theight & 0XFF);
    }
}

/**
 * @brief       Initialize LCD
 * @note        This initialization function can initialize various types of LCDs (see description at the beginning of this .c file)
 *
 * @param       None
 * @retval      None
 */
void lcd_init(void)
{
    LCD_CS_GPIO_CLK_ENABLE();   /* LCD_CS pin clock enable */
    LCD_WR_GPIO_CLK_ENABLE();   /* LCD_WR pin clock enable */
    LCD_RD_GPIO_CLK_ENABLE();   /* LCD_RD pin clock enable */
    LCD_RS_GPIO_CLK_ENABLE();   /* LCD_RS pin clock enable */
    LCD_BL_GPIO_CLK_ENABLE();   /* LCD_BL pin clock enable */

    RCC->AHB1ENR |= 3 << 3;     /* Enable PD,PE */
    RCC->AHB3ENR |= 1 << 0;     /* Enable FSMC clock */

    // Ensure sys.h defines SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU
    sys_gpio_set(LCD_CS_GPIO_PORT, LCD_CS_GPIO_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);   /* LCD_CS pin mode setting */

    sys_gpio_set(LCD_WR_GPIO_PORT, LCD_WR_GPIO_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);   /* LCD_WR pin mode setting */

    sys_gpio_set(LCD_RD_GPIO_PORT, LCD_RD_GPIO_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);   /* LCD_RD pin mode setting */

    sys_gpio_set(LCD_RS_GPIO_PORT, LCD_RS_GPIO_PIN,
                 SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);   /* LCD_RS pin mode setting */

    // Ensure sys.h defines SYS_GPIO_MODE_OUT
    sys_gpio_set(LCD_BL_GPIO_PORT, LCD_BL_GPIO_PIN,
                 SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);  /* LCD_BL pin mode setting (push-pull output) */

    // Ensure sys.h declares sys_gpio_af_set function
    sys_gpio_af_set(LCD_CS_GPIO_PORT, LCD_CS_GPIO_PIN, 12);     /* LCD_CS pin, AF12 */
    sys_gpio_af_set(LCD_WR_GPIO_PORT, LCD_WR_GPIO_PIN, 12);     /* LCD_WR pin, AF12 */
    sys_gpio_af_set(LCD_RD_GPIO_PORT, LCD_RD_GPIO_PIN, 12);     /* LCD_RD pin, AF12 */
    sys_gpio_af_set(LCD_RS_GPIO_PORT, LCD_RS_GPIO_PIN, 12);     /* LCD_RS pin, AF12 */

    /* LCD_D0~LCD_D15 IO port initialization */
    sys_gpio_set(GPIOD, (3 << 0) | (7 << 8) | (3 << 14), SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);   /* PD0,1,8,9,10,14,15   AF OUT */
    sys_gpio_set(GPIOE, (0X1FF << 7), SYS_GPIO_MODE_AF, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);                      /* PE7~15  AF OUT */

    sys_gpio_af_set(GPIOD, (3 << 0) | (7 << 8) | (3 << 14), 12);/* PD0,1,8,9,10,14,15   AF12 */
    sys_gpio_af_set(GPIOE, (0X1FF << 7), 12);                   /* PE7~15  AF12 */

    /* FSMC clock comes from HCLK, frequency is 168Mhz
     * Register clear
     * bank1 has NE1~4, each has a BCR+TCR, so there are a total of eight registers.
     * Here we use NE4, which corresponds to BTCR[6], [7]
     */
    LCD_FSMC_BCRX = 0X00000000; /* BCR register clear */
    LCD_FSMC_BTRX = 0X00000000; /* BTR register clear */
    LCD_FSMC_BWTRX = 0X00000000;/* BWTR register clear */

    /* Set BCR register to use asynchronous mode */
    LCD_FSMC_BCRX |= 1 << 12;   /* Memory write enable */
    LCD_FSMC_BCRX |= 1 << 14;   /* Read and write use different timings */
    LCD_FSMC_BCRX |= 1 << 4;    /* Memory data width is 16bit */

    /* Set BTR register, read timing control register */
    LCD_FSMC_BTRX |= 0 << 28;   /* Mode A */
    LCD_FSMC_BTRX |= 15 << 0;   /* Address setup time (ADDSET) is 15 HCLK cycles 1/168M = 6ns * 15 = 90ns */

    /* Because the read data speed of the LCD driver IC cannot be too fast, especially for some strange chips */
    LCD_FSMC_BTRX |= 60 << 8;   /* Data hold time (DATAST) is 60 HCLK cycles = 6ns * 60 = 360ns */

    /* Write timing control register */
    LCD_FSMC_BWTRX |= 0 << 28;  /* Mode A */
    LCD_FSMC_BWTRX |= 9 << 0;   /* Address setup time (ADDSET) is 9 HCLK cycles = 6ns * 9 = 54ns */

    /* The write signal pulse width of some LCD driver ICs must be at least 50ns. */
    LCD_FSMC_BWTRX |= 8 << 8;   /* Data hold time (DATAST) is 8 HCLK cycles = 6ns * 8 = 48ns */

    /* Enable BANK, area x */
    LCD_FSMC_BCRX |= 1 << 0;    /* Enable BANK, area x */

    // ??: ? delay_ms(0X1FFFFF); ??? lcd_opt_delay(0X1FFFFF);
    // ?? 0X1FFFFF ???????????,??????????????
    lcd_opt_delay(0X1FFFFF);     /* ??? FSMC ?,??????????????? */

    /* Try to read 9341 ID */
    lcd_wr_regno(0XD3);
    lcddev.id = lcd_rd_data();  /* dummy read */
    lcddev.id = lcd_rd_data();  /* Read 0X00 */
    lcddev.id <<= 8;
    lcddev.id |= lcd_rd_data(); /* Read 0X41 */

    if (lcddev.id != 0X9341)    /* Not 9341, try to see if it is ST7789 */
    {
        lcd_wr_regno(0X04);
        lcddev.id = lcd_rd_data();      /* dummy read */
        lcddev.id = lcd_rd_data();      /* Read 0X85 */
        lcddev.id = lcd_rd_data();      /* Read 0X85 */
        lcddev.id <<= 8;
        lcddev.id |= lcd_rd_data();     /* Read 0X52 */

        if (lcddev.id == 0X8552)        /* Convert 8552 ID to 7789 */
        {
            lcddev.id = 0x7789;
        }

        if (lcddev.id != 0x7789)        /* Not ST7789 either, try if it is NT35310 */
        {
            lcd_wr_regno(0XD4);
            lcddev.id = lcd_rd_data();  /* dummy read */
            lcddev.id = lcd_rd_data();  /* Read 0X01 */
            lcddev.id = lcd_rd_data();  /* Read 0X53 */
            lcddev.id <<= 8;
            lcddev.id |= lcd_rd_data(); /* Read 0X10 here */

            if (lcddev.id != 0X5310)    /* Not NT35310 either, try if it is ST7796 */
            {
                lcd_wr_regno(0XD3);
                lcddev.id = lcd_rd_data();  /* dummy read */
                lcddev.id = lcd_rd_data();  /* Read 0X00 */
                lcddev.id = lcd_rd_data();  /* Read 0X77 */
                lcddev.id <<= 8;
                lcddev.id |= lcd_rd_data(); /* Read 0X96 */

                if (lcddev.id != 0x7796)    /* Not ST7796 either, try if it is NT35510 */
                {
                    /* Send secret key (provided by manufacturer, just copy) */
                    lcd_write_reg(0xF000, 0x0055);
                    lcd_write_reg(0xF001, 0x00AA);
                    lcd_write_reg(0xF002, 0x0052);
                    lcd_write_reg(0xF003, 0x0008);
                    lcd_write_reg(0xF004, 0x0001);

                    lcd_wr_regno(0xC500);           /* Read ID high 8 bits */
                    lcddev.id = lcd_rd_data();      /* Read 0X55 */
                    lcddev.id <<= 8;

                    lcd_wr_regno(0xC501);           /* Read ID low 8 bits */
                    lcddev.id |= lcd_rd_data();     /* Read 0X10 */

                    delay_ms(5);                    /* Wait 5ms, because 0XC501 command is software reset command for 1963, wait 5ms for 1963 to reset OK before operating */

                    if (lcddev.id != 0X5510)        /* Not NT5510 either, try if it is ILI9806 */
                    {
                        lcd_wr_regno(0XD3);
                        lcddev.id = lcd_rd_data();  /* dummy read */
                        lcddev.id = lcd_rd_data();  /* Read 0X00 */
                        lcddev.id = lcd_rd_data();  /* Read 0X98 */
                        lcddev.id <<= 8;
                        lcddev.id |= lcd_rd_data(); /* Read 0X06 */

                        if (lcddev.id != 0x9806)    /* Not ILI9806 either, try if it is SSD1963 */
                        {
                            lcd_wr_regno(0XA1);
                            lcddev.id = lcd_rd_data();
                            lcddev.id = lcd_rd_data();  /* Read 0X57 */
                            lcddev.id <<= 8;
                            lcddev.id |= lcd_rd_data(); /* Read 0X61 */

                            if (lcddev.id == 0X5761) lcddev.id = 0X1963; /* SSD1963 read ID is 5761H, for easy distinction, we force it to 1963 */
                        }
                    }
                }
            }
        }
    }

    /* Special note, if serial port 1 initialization is blocked in the main function, it will get stuck in printf
     * (stuck in f_putc function), so, serial port 1 must be initialized, or the following printf statement must be blocked!
     */
    // printf("LCD ID:%x\r\n", lcddev.id); /* Print LCD ID - 已注释掉，只保留ADC数据输出 */

    if (lcddev.id == 0X7789)
    {
        lcd_ex_st7789_reginit();    /* Execute ST7789 initialization */
    }
    else if (lcddev.id == 0X9341)
    {
        lcd_ex_ili9341_reginit();   /* Execute ILI9341 initialization */
    }
    else if (lcddev.id == 0x5310)
    {
        lcd_ex_nt35310_reginit();   /* Execute NT35310 initialization */
    }
    else if (lcddev.id == 0x7796)
    {
        lcd_ex_st7796_reginit();    /* Execute ST7796 initialization */
    }
    else if (lcddev.id == 0x5510)
    {
        lcd_ex_nt35510_reginit();   /* Execute NT35510 initialization */
    }
    else if (lcddev.id == 0x9806)
    {
        lcd_ex_ili9806_reginit();   /* Execute ILI9806 initialization */
    }
    else if (lcddev.id == 0X1963)
    {
        lcd_ex_ssd1963_reginit();   /* Execute SSD1963 initialization */
        lcd_ssd_backlight_set(100); /* Set backlight to brightest */
    }

    /* Due to different write timings for different screens, the timing here can be modified according to your screen
      (if a long ribbon cable is plugged in, it will also affect the timing, you need to modify it according to the situation) */
    /* After initialization, speed up */
    if (lcddev.id == 0X7789)
    {
        /* Reconfigure the timing of the write timing control register */
        LCD_FSMC_BWTRX &= ~(0XF << 0);  /* Clear address setup time (ADDSET) */
        LCD_FSMC_BWTRX &= ~(0XF << 8);  /* Clear data hold time */
        LCD_FSMC_BWTRX |= 3 << 0;       /* Address setup time (ADDSET) is 3 HCLK cycles = 18ns */
        LCD_FSMC_BWTRX |= 3 << 8;       /* Data hold time (DATAST) is 3 HCLK cycles = 18ns */
    }
    else if ( lcddev.id == 0X9806 || lcddev.id == 0X9341 || lcddev.id == 0X5510)
    {
        /* Reconfigure the timing of the write timing control register */
        LCD_FSMC_BWTRX &= ~(0XF << 0);  /* Clear address setup time (ADDSET) */
        LCD_FSMC_BWTRX &= ~(0XF << 8);  /* Clear data hold time */
        LCD_FSMC_BWTRX |= 2 << 0;       /* Address setup time (ADDSET) is 2 HCLK cycles = 12ns */
        LCD_FSMC_BWTRX |= 2 << 8;       /* Data hold time (DATAST) is 2 HCLK cycles = 12ns */
    }
    else if (lcddev.id == 0X5310 || lcddev.id == 0X7796 || lcddev.id == 0X1963)
    {
        /* Reconfigure the timing of the write timing control register */
        LCD_FSMC_BWTRX &= ~(0XF << 0);  /* Clear address setup time (ADDSET) */
        LCD_FSMC_BWTRX &= ~(0XF << 8);  /* Clear data hold time */
        LCD_FSMC_BWTRX |= 1 << 0;       /* Address setup time (ADDSET) is 1 HCLK cycle = 6ns */
        LCD_FSMC_BWTRX |= 1 << 8;       /* Data hold time (DATAST) is 1 HCLK cycle = 6ns */
    }

    lcd_display_dir(0); /* ???? */
    LCD_BL(1);          /* ???? */
    lcd_clear(WHITE);
}

/**
 * @brief       ????
 * @param       color: ????
 * @retval      ?
 */
void lcd_clear(uint16_t color)
{
    uint32_t index = 0;
    uint32_t totalpoint = lcddev.width;
    totalpoint *= lcddev.height;    /* ????? */

    lcd_set_cursor(0x00, 0x0000);   /* ?????? */
    lcd_write_ram_prepare();        /* ???? GRAM */

    for (index = 0; index < totalpoint; index++)
    {
        LCD->LCD_RAM = color;
   }
}

/**
 * @brief       ???????????
 * @param       (sx,sy),(ex,ey): ??????????,????:(ex - sx + 1) * (ey - sy + 1)
 * @param       color: ???? (32???,?? LTDC)
 * @retval      ?
 */
void lcd_fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color)
{
    uint16_t i, j;
    uint16_t xlen = 0;
    xlen = ex - sx + 1;

    for (i = sy; i <= ey; i++)
    {
        lcd_set_cursor(sx, i);      /* ?????? */
        lcd_write_ram_prepare();    /* ???? GRAM */

        for (j = 0; j < xlen; j++)
        {
            LCD->LCD_RAM = color;   /* ???? */
        }
    }
}

/**
 * @brief       ????????????
 * @param       (sx,sy),(ex,ey): ??????????,????:(ex - sx + 1) * (ey - sy + 1)
 * @param       color: ????????????
 * @retval      ?
 */
void lcd_color_fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint16_t *color)
{
    uint16_t height, width;
    uint16_t i, j;
    width = ex - sx + 1;            /* ?????? */
    height = ey - sy + 1;           /* ?? */

    for (i = 0; i < height; i++)
    {
        lcd_set_cursor(sx, sy + i); /* ?????? */
        lcd_write_ram_prepare();    /* ???? GRAM */

        for (j = 0; j < width; j++)
        {
            LCD->LCD_RAM = color[i * width + j]; /* ???? */
        }
    }
}

/**
 * @brief       ??
 * @param       x1,y1: ????
 * @param       x2,y2: ????
 * @param       color: ????
 * @retval      ?
 */
void lcd_draw_line(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    uint16_t t;
    int xerr = 0, yerr = 0, delta_x, delta_y, distance;
    int incx, incy, row, col;
    delta_x = x2 - x1;          /* ?????? */
    delta_y = y2 - y1;
    row = x1;
    col = y1;

    if (delta_x > 0) incx = 1;          /* ?????? */
    else if (delta_x == 0) incx = 0;    /* ??? */
    else
    {
        incx = -1;
        delta_x = -delta_x;
    }

    if (delta_y > 0) incy = 1;
    else if (delta_y == 0) incy = 0;    /* ??? */
    else
    {
        incy = -1;
        delta_y = -delta_y;
    }

    if ( delta_x > delta_y) distance = delta_x; /* ????????? */
    else distance = delta_y;

    for (t = 0; t <= distance + 1; t++ )    /* ???? */
    {
        lcd_draw_point(row, col, color);    /* ?? */
        xerr += delta_x ;
        yerr += delta_y ;

        if (xerr > distance)
        {
            xerr -= distance;
            row += incx;
        }

        if (yerr > distance)
        {
            yerr -= distance;
            col += incy;
        }
    }
}

/**
 * @brief       ????
 * @param       x0,y0: ????
 * @param       len  : ????
 * @param       color: ????
 * @retval      ?
 */
void lcd_draw_hline(uint16_t x, uint16_t y, uint16_t len, uint16_t color)
{
    if ((len == 0) || (x > lcddev.width) || (y > lcddev.height)) return;

    lcd_fill(x, y, x + len - 1, y, color);
}

/**
 * @brief       ???
 * @param       x1,y1: ????
 * @param       x2,y2: ????
 * @param       color: ????
 * @retval      ?
 */
void lcd_draw_rectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    lcd_draw_line(x1, y1, x2, y1, color);
    lcd_draw_line(x1, y1, x1, y2, color);
    lcd_draw_line(x1, y2, x2, y2, color);
    lcd_draw_line(x2, y1, x2, y2, color);
}

/**
 * @brief       ??
 * @param       x0,y0: ????
 * @param       r    : ??
 * @param       color: ????
 * @retval      ?
 */
void lcd_draw_circle(uint16_t x0, uint16_t y0, uint8_t r, uint16_t color)
{
    int a, b;
    int di;
    a = 0;
    b = r;
    di = 3 - (r << 1);       /* ??????????? */

    while (a <= b)
    {
        lcd_draw_point(x0 + a, y0 - b, color);  /* 5 */
        lcd_draw_point(x0 + b, y0 - a, color);  /* 0 */
        lcd_draw_point(x0 + b, y0 + a, color);  /* 4 */
        lcd_draw_point(x0 + a, y0 + b, color);  /* 6 */
        lcd_draw_point(x0 - a, y0 + b, color);  /* 1 */
        lcd_draw_point(x0 - b, y0 + a, color);
        lcd_draw_point(x0 - a, y0 - b, color);  /* 2 */
        lcd_draw_point(x0 - b, y0 - a, color);  /* 7 */
        a++;

        /* ?? Bresenham ???? */
        if (di < 0)
        {
            di += 4 * a + 6;
        }
        else
        {
            di += 10 + 4 * (a - b);
            b--;
        }
    }
}

/**
 * @brief       ?????
 * @param       x,y  : ????
 * @param       r    : ??
 * @param       color: ????
 * @retval      ?
 */
void lcd_fill_circle(uint16_t x, uint16_t y, uint16_t r, uint16_t color)
{
    uint32_t i;
    uint32_t imax = ((uint32_t)r * 707) / 1000 + 1;
    uint32_t sqmax = (uint32_t)r * (uint32_t)r + (uint32_t)r / 2;
    uint32_t xr = r;

    lcd_draw_hline(x - r, y, 2 * r, color);

    for (i = 1; i <= imax; i++)
    {
        if ((i * i + xr * xr) > sqmax)
        {
            /* ????? */
            if (xr > imax)
            {
                lcd_draw_hline (x - i + 1, y + xr, 2 * (i - 1), color);
                lcd_draw_hline (x - i + 1, y - xr, 2 * (i - 1), color);
            }

            xr--;
        }

        /* ??? (??) ?? */
        lcd_draw_hline(x - xr, y + i, 2 * xr, color);
        lcd_draw_hline(x - xr, y - i, 2 * xr, color);
    }
}

/**
 * @brief       ???????????
 * @param       x,y   : ??
 * @param       chr   : ??????: " "--->"~"
 * @param       size  : ???? 12/16/24/32
 * @param       mode  : ???? (1); ????? (0);
 * @param       color : ????;
 * @retval      ?
 */
void lcd_show_char(uint16_t x, uint16_t y, char chr, uint8_t size, uint8_t mode, uint16_t color)
{
    uint8_t temp, t1, t;
    uint16_t y0 = y;
    uint8_t csize = 0;
    uint8_t *pfont = 0;

    csize = (size / 8 + ((size % 8) ? 1 : 0)) * (size / 2); /* ???????????? */
    chr = chr - ' ';    /* ????? (ASCII ????????,?? -' ' ?????????) */

    switch (size)
    {
        case 12:
            pfont = (uint8_t *)asc2_1206[chr];  /* ?? 1206 ?? */
            break;

        case 16:
            pfont = (uint8_t *)asc2_1608[chr];  /* ?? 1608 ?? */
            break;

        case 24:
            pfont = (uint8_t *)asc2_2412[chr];  /* ?? 2412 ?? */
            break;

        case 32:
            pfont = (uint8_t *)asc2_3216[chr];  /* ?? 3216 ?? */
            break;

        default:
            return ;
    }

    for (t = 0; t < csize; t++)
    {
        temp = pfont[t];    /* ???????? */

        for (t1 = 0; t1 < 8; t1++)  /* ??? 8 ?? */
        {
            if (temp & 0x80)        /* ???,???? */
            {
                lcd_draw_point(x, y, color);        /* ??,???? */
            }
            else if (mode == 0)     /* ???,??? */
            {
                lcd_draw_point(x, y, g_back_color); /* ????,???????? (????????????) */
            }

            temp <<= 1; /* ??,???????? */
            y++;

            if (y >= lcddev.height) return;     /* ???? */

            if ((y - y0) == size)   /* ???????? */
            {
                y = y0; /* y ???? */
                x++;    /* x ???? */

                if (x >= lcddev.width) return;  /* x ?????? */

                break;
            }
        }
    }
}

/**
 * @brief       ???,m^n
 * @param       m: ??
 * @param       n: ??
 * @retval      m ? n ??
 */
static uint32_t lcd_pow(uint8_t m, uint8_t n)
{
    uint32_t result = 1;

    while (n--) result *= m;

    return result;
}

/**
 * @brief       ?? len ???
 * @param       x,y   : ????
 * @param       num   : ? (0 ~ 2^32)
 * @param       len   : ??????
 * @param       size  : ???? 12/16/24/32
 * @param       color : ????;
 * @retval      ?
 */
void lcd_show_num(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint16_t color)
{
    uint8_t t, temp;
    uint8_t enshow = 0;

    for (t = 0; t < len; t++)   /* ???????? */
    {
        temp = (num / lcd_pow(10, len - t - 1)) % 10;   /* ????????? */

        if (enshow == 0 && t < (len - 1))   /* ?????,???????? */
        {
            if (temp == 0)
            {
                lcd_show_char(x + (size / 2) * t, y, ' ', size, 0, color);  /* ????,??? */
                continue;   /* ??????? */
            }
            else
            {
                enshow = 1; /* ???? */
            }

        }

        lcd_show_char(x + (size / 2) * t, y, temp + '0', size, 0, color);   /* ???? */
    }
}

/**
 * @brief       ???? len ??? (?????)
 * @param       x,y   : ????
 * @param       num   : ? (0 ~ 2^32)
 * @param       len   : ??????
 * @param       size  : ???? 12/16/24/32
 * @param       mode  : ????
 * [7]:0, ???; 1, ?? 0?
 * [6:1]: ??
 * [0]:0, ?????; 1, ?????
 * @param       color : ????;
 * @retval      ?
 */
void lcd_show_xnum(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint8_t mode, uint16_t color)
{
    uint8_t t, temp;
    uint8_t enshow = 0;

    for (t = 0; t < len; t++)   /* ???????? */
    {
        temp = (num / lcd_pow(10, len - t - 1)) % 10;    /* ????????? */

        if (enshow == 0 && t < (len - 1))   /* ?????,???????? */
        {
            if (temp == 0)
            {
                if (mode & 0X80)   /* ?????? 0 */
                {
                    lcd_show_char(x + (size / 2) * t, y, '0', size, mode & 0X01, color);  /* ? 0 ?? */
                }
                else
                {
                    lcd_show_char(x + (size / 2) * t, y, ' ', size, mode & 0X01, color);  /* ????? */
                }

                continue;
            }
            else
            {
                enshow = 1; /* ???? */
            }

        }

        lcd_show_char(x + (size / 2) * t, y, temp + '0', size, mode & 0X01, color);
    }
}

/**
 * @brief       ?????
 * @param       x,y         : ????
 * @param       width,height: ????
 * @param       size        : ???? 12/16/24/32
 * @param       p           : ?????????
 * @param       color       : ?????;
 * @retval      ?
 */
void lcd_show_string(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t size, char *p, uint16_t color)
{
    uint8_t x0 = x;
    width += x;
    height += y;

    while ((*p <= '~') && (*p >= ' '))   /* ?????????! */
    {
        if (x >= width)
        {
            x = x0;
            y += size;
        }

        if (y >= height) break; /* ?? */

        lcd_show_char(x, y, *p, size, 0, color);
        x += size / 2;
        p++;
    }
}
