..\obj\touch.o: ..\HARDWARE\TOUCH\touch.c
..\obj\touch.o: ..\HARDWARE\TOUCH\touch.h
..\obj\touch.o: ..\USER\stm32f4xx.h
..\obj\touch.o: ..\CORE\core_cm4.h
..\obj\touch.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\touch.o: ..\CORE\core_cmInstr.h
..\obj\touch.o: ..\CORE\core_cmFunc.h
..\obj\touch.o: ..\CORE\core_cm4_simd.h
..\obj\touch.o: ..\USER\system_stm32f4xx.h
..\obj\touch.o: ..\USER\stm32f4xx_conf.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\touch.o: ..\USER\stm32f4xx.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\touch.o: ..\FWLIB\inc\misc.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\touch.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\touch.o: ..\SYSTEM\sys\sys.h
..\obj\touch.o: ..\SYSTEM\delay\delay.h
..\obj\touch.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
..\obj\touch.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\touch.o: ..\HARDWARE\LCD\lcd.h
..\obj\touch.o: ..\SYSTEM\usart\usart.h
..\obj\touch.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
