{"configurations": [{"name": "Template", "includePath": ["c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\CORE", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\SYSTEM\\delay", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\SYSTEM\\sys", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\SYSTEM\\usart", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\USER", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\FWLIB\\inc", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\key", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\LED", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\G", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\TIMER2", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\DSP_LIB", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\DSP_LIB\\Include", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\FFT", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\ADC", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\KALMAN", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\G1", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\HARDWARE\\AD9833", "D:\\keil_5\\ARM\\ARMCC\\include", "D:\\keil_5\\ARM\\ARMCC\\include\\rw", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环\\FWLIB\\src", "c:\\Users\\<USER>\\Desktop\\信号分离装置\\锁相环"], "defines": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER", "ARM_MATH_CM4", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}