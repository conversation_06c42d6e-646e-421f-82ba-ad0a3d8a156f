..\obj\touch_1.o: ..\HARDWARE\TOUCH\touch.c
..\obj\touch_1.o: ..\HARDWARE\TOUCH\touch.h
..\obj\touch_1.o: ..\USER\stm32f4xx.h
..\obj\touch_1.o: ..\CORE\core_cm4.h
..\obj\touch_1.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\touch_1.o: ..\CORE\core_cmInstr.h
..\obj\touch_1.o: ..\CORE\core_cmFunc.h
..\obj\touch_1.o: ..\CORE\core_cm4_simd.h
..\obj\touch_1.o: ..\USER\system_stm32f4xx.h
..\obj\touch_1.o: ..\USER\stm32f4xx_conf.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\touch_1.o: ..\USER\stm32f4xx.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\touch_1.o: ..\FWLIB\inc\misc.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\touch_1.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\touch_1.o: ..\SYSTEM\sys\sys.h
..\obj\touch_1.o: ..\SYSTEM\delay\delay.h
..\obj\touch_1.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
..\obj\touch_1.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\touch_1.o: ..\HARDWARE\LCD\lcd.h
..\obj\touch_1.o: ..\SYSTEM\usart\usart.h
..\obj\touch_1.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
