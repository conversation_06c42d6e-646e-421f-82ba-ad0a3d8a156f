/**
 ****************************************************************************************************
 * @file        touch.c
 * <AUTHOR>
 * @version     V2.0
 * @date        2024-01-01
 * @brief       4.3寸电容式触摸屏-FT5206 驱动代码
 ****************************************************************************************************
 */

#include "touch.h"
#include "lcd.h"
#include "delay.h"
#include "usart.h"
#include "stm32f4xx_i2c.h"

_m_tp_dev tp_dev =
{
    tp_init,
    tp_scan,
    tp_adjust,
    0,
    0,
    0,
    0,
    0,
    {0,0,0,0,0},
    {0,0,0,0,0},
    1.0,    /* xfac - 电容屏不需要校准 */
    1.0,    /* yfac - 电容屏不需要校准 */
    0,      /* xoff */
    0,      /* yoff */
    1,      /* touchtype - 1表示电容屏 */
};

/* FT5206寄存器定义 */
#define FT_DEVIDE_MODE      0x00    /* 设备模式寄存器 */
#define FT_REG_NUM_FINGER   0x02    /* 触摸点个数寄存器 */
#define FT_TP1_REG          0X03    /* 第一个触摸点数据地址 */
#define FT_TP2_REG          0X09    /* 第二个触摸点数据地址 */
#define FT_TP3_REG          0X0F    /* 第三个触摸点数据地址 */
#define FT_TP4_REG          0X15    /* 第四个触摸点数据地址 */
#define FT_TP5_REG          0X1B    /* 第五个触摸点数据地址 */
#define FT_ID_G_LIB_VERSION 0xA1    /* 版本寄存器 */
#define FT_ID_G_MODE        0xA4    /* 中断模式寄存器 */
#define FT_ID_G_THGROUP     0x80    /* 触摸有效值设置寄存器 */
#define FT_ID_G_PERIODACTIVE 0x88   /* 激活状态周期设置寄存器 */

/**
 * @brief       模拟I2C开始信号
 * @param       无
 * @retval      无
 */
void ct_i2c_start(void)
{
    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 1);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
    delay_us(4);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 0);
    delay_us(4);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
}

/**
 * @brief       模拟I2C停止信号
 * @param       无
 * @retval      无
 */
void ct_i2c_stop(void)
{
    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 0);
    delay_us(4);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 1);
    delay_us(4);
}

/**
 * @brief       等待应答信号
 * @param       无
 * @retval      1: 接收应答失败; 0: 接收应答成功
 */
uint8_t ct_i2c_wait_ack(void)
{
    uint8_t ucErrTime = 0;

    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_IN, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 1);
    delay_us(1);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
    delay_us(1);

    while (sys_gpio_pin_get(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN))
    {
        ucErrTime++;
        if (ucErrTime > 250)
        {
            ct_i2c_stop();
            return 1;
        }
    }

    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
    return 0;
}

/**
 * @brief       产生ACK应答
 * @param       无
 * @retval      无
 */
void ct_i2c_ack(void)
{
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 0);
    delay_us(2);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
    delay_us(2);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
}

/**
 * @brief       不产生ACK应答
 * @param       无
 * @retval      无
 */
void ct_i2c_nack(void)
{
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 1);
    delay_us(2);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
    delay_us(2);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
}

/**
 * @brief       I2C发送一个字节
 * @param       txd: 发送的数据
 * @retval      无
 */
void ct_i2c_send_byte(uint8_t txd)
{
    uint8_t t;

    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);

    for (t = 0; t < 8; t++)
    {
        if ((txd & 0x80) >> 7)
            sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 1);
        else
            sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 0);

        txd <<= 1;
        delay_us(2);
        sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
        delay_us(2);
        sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
        delay_us(2);
    }
}

/**
 * @brief       I2C读取一个字节
 * @param       ack: ack=1时，发送ACK，ack=0，发送nACK
 * @retval      接收到的数据
 */
uint8_t ct_i2c_read_byte(unsigned char ack)
{
    unsigned char i, receive = 0;

    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_IN, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    for (i = 0; i < 8; i++)
    {
        sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 0);
        delay_us(2);
        sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
        receive <<= 1;

        if (sys_gpio_pin_get(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN))
            receive++;

        delay_us(1);
    }

    if (!ack)
        ct_i2c_nack();
    else
        ct_i2c_ack();

    return receive;
}

/**
 * @brief       FT5206写寄存器
 * @param       reg: 寄存器地址
 * @param       buf: 数据缓冲区
 * @param       len: 写数据长度
 * @retval      0: 成功; 1: 失败
 */
uint8_t ft5206_wr_reg(uint16_t reg, uint8_t *buf, uint8_t len)
{
    uint8_t i;
    uint8_t ret = 0;

    ct_i2c_start();
    ct_i2c_send_byte(FT5206_ADDR);
    ct_i2c_wait_ack();
    ct_i2c_send_byte(reg & 0XFF);
    ct_i2c_wait_ack();

    for (i = 0; i < len; i++)
    {
        ct_i2c_send_byte(buf[i]);
        ret = ct_i2c_wait_ack();
        if (ret) break;
    }

    ct_i2c_stop();
    return ret;
}

/**
 * @brief       FT5206读寄存器
 * @param       reg: 寄存器地址
 * @param       buf: 数据缓冲区
 * @param       len: 读数据长度
 * @retval      无
 */
void ft5206_rd_reg(uint16_t reg, uint8_t *buf, uint8_t len)
{
    uint8_t i;

    ct_i2c_start();
    ct_i2c_send_byte(FT5206_ADDR);
    ct_i2c_wait_ack();
    ct_i2c_send_byte(reg & 0XFF);
    ct_i2c_wait_ack();
    ct_i2c_start();
    ct_i2c_send_byte(FT5206_ADDR + 1);
    ct_i2c_wait_ack();

    for (i = 0; i < len; i++)
    {
        buf[i] = ct_i2c_read_byte(i == (len - 1) ? 0 : 1);
    }

    ct_i2c_stop();
}

/**
 * @brief       触摸屏扫描函数（电容屏）
 * @param       mode: 扫描模式
 * @retval      0: 无触摸; 1: 有触摸
 */
uint8_t tp_scan(uint8_t mode)
{
    uint8_t buf[4];
    uint8_t i = 0;
    uint8_t res = 0;
    uint8_t temp;
    static uint8_t t = 0;

    t++;
    if ((t % 10) == 0 || t < 10)  // 空闲时,每进入10次CTP_Scan函数才检测1次,从而节省CPU使用率
    {
        ft5206_rd_reg(FT_REG_NUM_FINGER, &temp, 1);  // 读取触摸点的状态
        if ((temp & 0XF) && ((temp & 0XF) < 6))
        {
            temp = 0XFF << (temp & 0XF);  // 将点的个数转换为1的位数,匹配tp_dev.sta定义
            tp_dev.sta = (~temp) | TP_PRES_DOWN | TP_CATH_PRES;

            for (i = 0; i < 5; i++)
            {
                if (tp_dev.sta & (1 << i))  // 触摸有效?
                {
                    ft5206_rd_reg(FT_TP1_REG + i * 6, buf, 4);  // 读取XY坐标值

                    if (tp_dev.touchtype & 0X01)  // 横屏
                    {
                        tp_dev.xt[i] = ((uint16_t)(buf[0] & 0X0F) << 8) + buf[1];
                        tp_dev.yt[i] = ((uint16_t)(buf[2] & 0X0F) << 8) + buf[3];
                    }
                    else
                    {
                        tp_dev.yt[i] = lcddev.width - (((uint16_t)(buf[0] & 0X0F) << 8) + buf[1]);
                        tp_dev.xt[i] = ((uint16_t)(buf[2] & 0X0F) << 8) + buf[3];
                    }

                    if ((buf[0] & 0XF0) != 0X80)
                        tp_dev.xt[i] = tp_dev.yt[i] = 0;  // 必须是contact事件，才认为有效
                }
            }

            res = 1;

            if (tp_dev.xt[0] == 0 && tp_dev.yt[0] == 0) temp = 0;  // 读到的数据都是0,则忽略此次数据

            t = 0;  // 触发一次,则会最少连续监测10次,从而提高命中率
        }
    }

    if ((tp_dev.sta & TP_PRES_DOWN) == 0)  // 之前没有被按下
    {
        tp_dev.xt[4] = tp_dev.xt[0];  // 保存触点0的数据
        tp_dev.yt[4] = tp_dev.yt[0];
    }
    else  // 之前已经被按下了
    {
        tp_dev.xt[4] = tp_dev.xt[0];  // 保存触点0的数据
        tp_dev.yt[4] = tp_dev.yt[0];
    }

    if ((mode & 0X1) == 0)  // 非连续读取模式
    {
        if (res == 0) tp_dev.sta = 0;  // 没有按键按下了.
        else t = 0;  // 有按键按下了
    }

    return res;
}

/**
 * @brief       触摸屏初始化
 * @param       无
 * @retval      0: 初始化失败; 1: 初始化成功
 */
uint8_t tp_init(void)
{
    uint8_t temp[2];

    /* 初始化GPIO */
    CT_RST_GPIO_CLK_ENABLE();
    CT_INT_GPIO_CLK_ENABLE();
    CT_I2C_SCL_GPIO_CLK_ENABLE();
    CT_I2C_SDA_GPIO_CLK_ENABLE();

    /* 配置复位引脚 */
    sys_gpio_set(CT_RST_GPIO_PORT, CT_RST_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    /* 配置中断引脚 */
    sys_gpio_set(CT_INT_GPIO_PORT, CT_INT_GPIO_PIN, SYS_GPIO_MODE_IN, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    /* 配置I2C引脚 */
    sys_gpio_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);
    sys_gpio_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, SYS_GPIO_MODE_OUT, SYS_GPIO_OTYPE_PP, SYS_GPIO_SPEED_HIGH, SYS_GPIO_PUPD_PU);

    /* 复位触摸屏 */
    CT_RST(0);
    delay_ms(20);
    CT_RST(1);
    delay_ms(50);

    /* 初始化I2C引脚状态 */
    sys_gpio_pin_set(CT_I2C_SCL_GPIO_PORT, CT_I2C_SCL_GPIO_PIN, 1);
    sys_gpio_pin_set(CT_I2C_SDA_GPIO_PORT, CT_I2C_SDA_GPIO_PIN, 1);

    /* 检测FT5206 */
    ft5206_rd_reg(FT_ID_G_LIB_VERSION, temp, 2);
    if ((temp[0] == 0X30 && temp[1] == 0X03) || temp[1] == 0X01 || temp[1] == 0X02)  // 版本:0X3003/0X0001/0X0002
    {
        /* 设置触摸有效值,22，越小越灵敏 */
        temp[0] = 22;
        ft5206_wr_reg(FT_ID_G_THGROUP, temp, 1);

        /* 设置激活周期,不能小于12,最大14 */
        temp[0] = 12;
        ft5206_wr_reg(FT_ID_G_PERIODACTIVE, temp, 1);

        /* 读取版本号,参考值:0x3003 */
        ft5206_rd_reg(FT_ID_G_LIB_VERSION, temp, 2);

        /* 设置工作模式 */
        temp[0] = 0;
        ft5206_wr_reg(FT_DEVIDE_MODE, temp, 1);

        /* 设置为竖屏 */
        tp_dev.touchtype |= 0X80;

        return 1;
    }

    return 0;  /* 没有检测到FT5206 */
}

/**
 * @brief       触摸屏校准（电容屏不需要校准）
 * @param       无
 * @retval      无
 */
void tp_adjust(void)
{
    // 电容屏不需要校准
    lcd_show_string(50, 100, 200, 30, 16, "Capacitive Touch", BLACK);
    lcd_show_string(50, 120, 200, 30, 16, "No Calibration Needed", BLACK);
    delay_ms(2000);
    lcd_clear(WHITE);
}

/**
 * @brief       画一个大点(2*2的点)
 * @param       x,y: 坐标
 * @param       color: 颜色
 * @retval      无
 */
void tp_draw_big_point(uint16_t x, uint16_t y, uint16_t color)
{
    lcd_draw_point(x, y, color);     /* 中心点 */
    lcd_draw_point(x + 1, y, color);
    lcd_draw_point(x, y + 1, color);
    lcd_draw_point(x + 1, y + 1, color);
}

/* 这个重复的tp_init函数已删除，使用前面定义的版本 */

/* 删除重复的tp_scan函数 */

/* 删除重复的tp_draw_touch_point函数 */

/* 删除重复的tp_draw_big_point函数 */

/* 删除重复的tp_adjust函数 */
