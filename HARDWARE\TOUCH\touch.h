/**
 ****************************************************************************************************
 * @file        touch.h
 * <AUTHOR>
 * @version     V1.0
 * @date        2024-01-01
 * @brief       4.3寸电阻式触摸屏-XPT2046 驱动代码
 ****************************************************************************************************
 */

#ifndef __TOUCH_H
#define __TOUCH_H

#include "stm32f4xx.h"
#include "sys.h"
#include "delay.h"
#include "math.h"
#include "stdlib.h"

/******************************************************************************************/
/* 电容触摸屏芯片 连接引脚 定义 (I2C接口) */

/* 常用的电容屏I2C地址 */
#define FT5206_ADDR         0x70    /* FT5206的器件地址 */
#define GT911_ADDR          0xBA    /* GT911的器件地址 */

/* I2C引脚定义 (根据实际接线) */
#define CT_I2C_SCL_GPIO_PORT            GPIOD
#define CT_I2C_SCL_GPIO_PIN             SYS_GPIO_PIN6
#define CT_I2C_SCL_GPIO_CLK_ENABLE()    do{ RCC->AHB1ENR |= 1 << 3; }while(0)  /* PD口时钟使能 */

#define CT_I2C_SDA_GPIO_PORT            GPIOD
#define CT_I2C_SDA_GPIO_PIN             SYS_GPIO_PIN7
#define CT_I2C_SDA_GPIO_CLK_ENABLE()    do{ RCC->AHB1ENR |= 1 << 3; }while(0)  /* PD口时钟使能 */

/* 复位和中断引脚 (需要根据实际硬件确定) */
#define CT_RST_GPIO_PORT                GPIOD
#define CT_RST_GPIO_PIN                 SYS_GPIO_PIN3  /* 假设使用PD3作为复位 */
#define CT_RST_GPIO_CLK_ENABLE()        do{ RCC->AHB1ENR |= 1 << 3; }while(0)

#define CT_INT_GPIO_PORT                GPIOD
#define CT_INT_GPIO_PIN                 SYS_GPIO_PIN2  /* 假设使用PD2作为中断 */
#define CT_INT_GPIO_CLK_ENABLE()        do{ RCC->AHB1ENR |= 1 << 3; }while(0)

/******************************************************************************************/

/* IO操作宏定义 */
#define CT_RST(x)       sys_gpio_pin_set(CT_RST_GPIO_PORT, CT_RST_GPIO_PIN, x)  /* 复位脚 */
#define CT_INT          sys_gpio_pin_get(CT_INT_GPIO_PORT, CT_INT_GPIO_PIN)      /* 中断脚 */

/* 触摸屏控制器 */
#define TP_PRES_DOWN    0x8000  /* 触屏被按下 */
#define TP_CATH_PRES    0x4000  /* 有按键按下了 */

#define CT_MAX_TOUCH    5       /* 电容屏支持的点数,固定为5点 */

/* 触摸屏参数 */
typedef struct
{
    uint8_t (*init)(void);          /* 初始化触摸屏控制器 */
    uint8_t (*scan)(uint8_t);       /* 扫描触摸屏.0,屏幕扫描;1,物理坐标; */
    void (*adjust)(void);           /* 触摸屏校准 */
    uint16_t x0;                    /* 原始坐标(第一次按下时的坐标) */
    uint16_t y0;
    uint16_t x;                     /* 当前坐标(此次扫描时,触屏的坐标) */
    uint16_t y;
    uint8_t  sta;                   /* 笔的状态 */
                                    /* b15:按下1/松开0; */
                                    /* b14:0,没有按键按下;1,有按键按下. */
                                    /* b13~b10:保留 */
                                    /* b9~b0:电容触摸屏按下的点数(0,表示未按下,1表示按下) */
    /* 5点触控数据 用于电容屏 */
    uint16_t xt[CT_MAX_TOUCH];      /* 当前坐标 */
    uint16_t yt[CT_MAX_TOUCH];      /* 电容屏有最多5组坐标,电阻屏则用xt[0],yt[0]代表:此次扫描时,触屏的坐标,用
                                     * xt[4],yt[4]存储第一次按下时的坐标.
                                     */

    /* 新增校准参数 */
    float xfac;                     /* x坐标比例因子 */
    float yfac;                     /* y坐标比例因子 */
    short xoff;                     /* x坐标偏移量 */
    short yoff;                     /* y坐标偏移量 */
    uint8_t touchtype;
} _m_tp_dev;

extern _m_tp_dev tp_dev;    /* 触屏控制器在touch.c里面定义 */

/* 电容屏函数声明 */
void ct_i2c_start(void);                        /* I2C开始信号 */
void ct_i2c_stop(void);                         /* I2C停止信号 */
uint8_t ct_i2c_wait_ack(void);                  /* I2C等待应答 */
void ct_i2c_ack(void);                          /* I2C发送应答 */
void ct_i2c_nack(void);                         /* I2C发送非应答 */
void ct_i2c_send_byte(uint8_t txd);             /* I2C发送一个字节 */
uint8_t ct_i2c_read_byte(unsigned char ack);    /* I2C读取一个字节 */

uint8_t ft5206_wr_reg(uint16_t reg, uint8_t *buf, uint8_t len);  /* FT5206写寄存器 */
void ft5206_rd_reg(uint16_t reg, uint8_t *buf, uint8_t len);     /* FT5206读寄存器 */

uint8_t tp_scan(uint8_t mode);                  /* 扫描触摸屏 */
void tp_adjust(void);                           /* 触屏校准(电容屏不需要) */
void tp_draw_big_point(uint16_t x, uint16_t y, uint16_t color);   /* 画一个大点 */
uint8_t tp_init(void);                          /* 初始化触摸屏 */

#endif
